
 Syntax:

 WINDOW LOG on|off|toggle [<filename>]
 WINDOW LOGFILE <file>
 WINDOW NEW [HIDDEN|SPLIT|-right SPLIT]
 WINDOW CLOSE [<first> [<last>]]
 WINDOW REFNUM <number>
 WINDOW GOTO active|<number>|<name>
 WINDOW NEXT
 WINDOW LAST
 WINDOW PREVIOUS
 WINDOW LEVEL [<levels>]
 WINDOW IMMORTAL on|off|toggle
 WINDOW SERVER [-sticky | -unsticky] <tag>
 WINDOW ITEM PREV
 WINDOW ITEM NEXT
 WINDOW ITEM GOTO <number>|<name>
 WINDOW ITEM MOVE <number>|<name>
 WINDOW NUMBER [-sticky] <number>
 WINDOW NAME <name>
 WINDOW HISTORY [-clear] <name>
 WINDOW MOVE PREV
 WINDOW MOVE NEXT
 WINDOW MOVE FIRST
 WINDOW MOVE LAST
 WINDOW MOVE <number>|<direction>
 WINDOW LIST
 WINDOW THEME [-delete] [<name>]
 WINDOW HIDE [<number>|<name>]
 WINDOW SHOW [-right] <number>|<name>
 WINDOW GROW [-right] [<lines>|<columns>]
 WINDOW SHRINK [-right] [<lines>|<columns>]
 WINDOW SIZE [-right] <lines>|<columns>
 WINDOW BALANCE [-right]
 WINDOW UP [-directional]
 WINDOW DOWN [-directional]
 WINDOW LEFT [-directional]
 WINDOW RIGHT [-directional]
 WINDOW STICK [<ref#>] [ON|OFF]
 WINDOW MOVE LEFT [-directional]
 WINDOW MOVE RIGHT [-directional]
 WINDOW MOVE UP [-directional]
 WINDOW MOVE DOWN [-directional]
 WINDOW HIDELEVEL [<levels>]

 Parameters:

     LOG:          Turn on or off logging of the active window, optionally specifying the log file to use.
     LOGFILE:      Sets the location of the log file to use for window logging without starting to log.
     NEW:          Creates a new hidden or split window.
     CLOSE:        Closes the current window, the specified one or all windows in the given range.
     REFNUM:       Go to the window with the given number.
     GOTO:         Go to the window with activity, with the given nickname, channel or with the specified number.
     NEXT:         Go to the next window numerically.
     LAST:         Go to the previously active window.
     PREVIOUS:     Go to the previous window numerically.
     LEVEL:        Changes the text levels to display in the window, or query the current level.
     IMMORTAL:     Modifies or queries the window mortality status. Immortal windows have an extra protection against WINDOW CLOSE.
     SERVER:       Change the active server of the window or the server stickyness. If the server is sticky, it cannot be cycled with next_window_item/previous_window_item
     ITEM PREV:    Make the previous item in this window active.
     ITEM NEXT:    Make the next item in this window active.
     ITEM GOTO:    Change to the query with the specified nickname, channel with the given name or window item number.
     ITEM MOVE:    Move the active window item to another window, or move the channel or query item specified by their name to the current window.
     NUMBER:       Change the active window number to the specified number, swapping the window already in that place if required. With -sticky, protect the window number from renumbering done by windows_auto_renumber. (To re-set the sticky attribute, use WINDOW NUMBER again without -sticky.)
     NAME:         Change or clear the window name. Window names must be unique.
     HISTORY:      Set or clear a specific named history to use for this window. All windows with the same named history will share a history.
     MOVE PREV:    Move the window to the place of the numerically previous window. At the first position, move the window to the end and renumber the consecutive block that it was part of.
     MOVE NEXT:    Move the window to the place of the numerically next window. At the last position, move the window to the first position and renumber the consecutive block at first position (if any)
     MOVE FIRST:   Move the window to the first position. Any windows inbetween are moved to their numerically next positions.
     MOVE LAST:    Move the window to the last position. Any windows inbetween are moved to their numerically previous positions.
     MOVE:         Move the window to the specified number or the first number that is in use when moving the window in the direction of the specified position. Any windows inbetween are shifted towards the old position of the window (unused positions remain empty)
     LIST:         List all the windows.
     THEME:        Applies or removes a per-window theme.
     GROW:         Increase the size of the active split window by the specified number of lines.
     SHRINK:       Decrease the size of the active split window by the specified number of lines.
     SIZE:         Set the current split window size to the specified number of lines.
     BALANCE:      Balance the heights of all split windows.
     HIDE:         Hides the current split window, or the split window specified by number or item name.
     SHOW:         Show the window specified by number or item name as a new split windows. It is made sticky when autostick_split_windows is turned on.
     UP:           Set the split window left or above the current one active. At the top, wraps to the bottom.
     DOWN:         Set the split window right or below the current one active. At the bottom, wraps left.
     LEFT:         Go to the previous window numerically that is part of the current sticky group (or not part of any sticky group).
     RIGHT:        Go to the next window numerically that is part of the current sticky group (or not part of any sticky group).
     STICK:        Make the currently active window sticky, or stick the window specified by number to the currently visible split window. Or turn off stickyness of the currently active window or the window specified by number.
     HIDELEVEL:    Changes the levels of text lines that should be hidden from view, or query the current hidden level.
     MOVE LEFT:    Move the window to the numerically previous location inside the current sticky group.
     MOVE RIGHT:   Move the window to the numerically next location inside the current sticky group.
     MOVE UP:      Move the current window to the sticky group of the previous split window. If no sticky group remains, the split window collapses.
     MOVE DOWN:    Move the current window to the sticky group of the next split window. If no sticky group remains, the split window collapses.

     -right:       Makes the command work on the width instead of height, or create the split window to the right instead of top.
     -directional: Set the split window in the given direction to the current one active, or move the current window to the sticky group of the split window in the given direction. (If no sticky group remains, the split window collapses.)

    Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

    LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with `+' are added to the current levels. Levels listed starting with `-' are removed from the current levels. To clear the levels, start the new level setting with `NONE'. Levels listed starting with `^' are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

 Description:

     Manipulates the window layout and positioning attributes.

 Examples:

     /WINDOW CLOSE
     /WINDOW ITEM MOVE 10
     /WINDOW GOTO 15
     /WINDOW GOTO ACTIVE
     /WINDOW GOTO mike
     /WINDOW GOTO #irssi
     /WINDOW NEW HIDDEN
     /WINDOW LOG OFF
     /WINDOW LOG ON ~/logs/debug.log
     /WINDOW LEVEL -ALL +NOTICES
     /WINDOW HIDELEVEL ^JOINS ^PARTS ^QUITS
     /WINDOW LOGFILE ~/logs/notices.log

 See also: JOIN, LEVELS, LOG, QUERY

 Irssi commands:
 window balance window hidelevel window left    window name     window right  window stick
 window close   window history   window level   window new      window scroll window theme
 window down    window immortal  window list    window next     window server window up
 window goto    window item      window log     window number   window show
 window grow    window kill      window logfile window previous window shrink
 window hide    window last      window move    window refnum   window size




Window Commands
clear
Scrolls up the text in the window and fills the window with blank lines; you may want to use this to make new text start at the top of the window again.
lastlog
Searches the active window for a pattern and displays the result.
layout
Saves the layout of your window configuration; the next time you connect to the server, you will join the channels in the same window as before. …
scrollback
Manipulate the text in the window to go to the given line number, or clear the buffers. …
window
Manipulates the window layout and positioning attributes.


Page source
Toggle Light / Dark / Auto color theme
layout
Syntax
LAYOUT SAVE
LAYOUT RESET
Parameters
SAVE:
Saves your layout to the configuration.
RESET:
Removes the saved layout from the configuration.
Description
Saves the layout of your window configuration; the next time you connect to the server, you will join the channels in the same window as before.

This method enables you to keep the same window layout when you start Irssi the next time.

You will need to use the SAVE command to confirm and commit the changes into the configuration file.

Examples
/LAYOUT SAVE
/LAYOUT RESET
See also
SAVE, WINDOW

Page source
Toggle Light / Dark / Auto color theme
scrollback
Syntax
SCROLLBACK CLEAR [-all] [<refnum>]
SCROLLBACK LEVELCLEAR [-all] [-level <level>] [<refnum>]
SCROLLBACK GOTO <+|-linecount>|<linenum>|<timestamp>
SCROLLBACK HOME
SCROLLBACK END
SCROLLBACK REDRAW
Parameters
CLEAR:
Clears the screen and the buffer of all text.
LEVELCLEAR:
Clears the screen and the buffer of text matching the given levels.
GOTO:
Go to the given position.
HOME:
Go to the start of the buffer.
END:
Go to the end of the buffer.
-all:
Applies to all windows instead of only the active one.
-level:
The levels, separated by a comma, to match.
The line number, timestamp to jump to or the window reference number to clear.

Description
Manipulate the text in the window to go to the given line number, or clear the buffers.

The timestamp format is ‘[dd[.mm] | -<days ago>] hh:mi[:ss]’.

Examples
/SCROLLBACK CLEAR
/SCROLLBACK LEVELCLEAR -level NOTICES
/SCROLLBACK GOTO 100
/SCROLLBACK HOME
/SCROLLBACK END
See also
CLEAR, WINDOW

window
Subcommands
window/split window creation
window new
 
window close
 
window list
window hide
 
window show
window changing
window refnum
 
window goto
 
window next
window last
 
window previous
 
window left
window right
window attribute manipulation
window level
 
window immortal
 
window server
window number
 
window name
 
window history
window theme
 
window stick
 
window hidelevel
window items
window item prev
 
window item next
 
window item goto
window item move
window moving/number changing
window number
 
window move prev
 
window move next
window move first
 
window move last
 
window move
window move left
 
window move right
split window resizing
window grow
 
window shrink
 
window size
window balance
split window navigation
window up
 
window down
 
window left
window right
split window moving
window stick
 
window move left
 
window move right
window move up
 
window move down
logging
window log
 
window logfile
Parameters
-right:
Makes the command work on the width instead of height, or create the split window to the right instead of top.
-directional:
Set the split window in the given direction to the current one active, or move the current window to the sticky group of the split window in the given direction. (If no sticky group remains, the split window collapses.)
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
/WINDOW CLOSE
/WINDOW ITEM MOVE 10
/WINDOW GOTO 15
/WINDOW GOTO ACTIVE
/WINDOW GOTO mike
/WINDOW GOTO #irssi
/WINDOW NEW HIDDEN
/WINDOW LOG OFF
/WINDOW LOG ON ~/logs/debug.log
/WINDOW LEVEL -ALL +NOTICES
/WINDOW HIDELEVEL ^JOINS ^PARTS ^QUITS
/WINDOW LOGFILE ~/logs/notices.log
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel

window/split window creation
Syntax
WINDOW NEW [HIDDEN|SPLIT|-right SPLIT]
WINDOW CLOSE [<first> [<last>]]
WINDOW LIST
WINDOW HIDE [<number>|<name>]
WINDOW SHOW [-right] <number>|<name>
Parameters
NEW:
Creates a new hidden or split window.
CLOSE:
Closes the current window, the specified one or all windows in the given range.
LIST:
List all the windows.
HIDE:
Hides the current split window, or the split window specified by number or item name.
SHOW:
Show the window specified by number or item name as a new split windows. It is made sticky when autostick_split_windows is turned on.
-right:
Makes the command work on the width instead of height, or create the split window to the right instead of top.
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
/WINDOW CLOSE
/WINDOW NEW HIDDEN
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel

window changing
Syntax
WINDOW REFNUM <number>
WINDOW GOTO active|<number>|<name>
WINDOW NEXT
WINDOW LAST
WINDOW PREVIOUS
WINDOW LEFT [-directional]
WINDOW RIGHT [-directional]
Parameters
REFNUM:
Go to the window with the given number.
GOTO:
Go to the window with activity, with the given nickname, channel or with the specified number.
NEXT:
Go to the next window numerically.
LAST:
Go to the previously active window.
PREVIOUS:
Go to the previous window numerically.
LEFT:
Go to the previous window numerically that is part of the current sticky group (or not part of any sticky group).
RIGHT:
Go to the next window numerically that is part of the current sticky group (or not part of any sticky group).
-directional:
Set the split window in the given direction to the current one active, or move the current window to the sticky group of the split window in the given direction. (If no sticky group remains, the split window collapses.)
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
/WINDOW GOTO 15
/WINDOW GOTO ACTIVE
/WINDOW GOTO mike
/WINDOW GOTO #irssi
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel

Page source
Toggle Light / Dark / Auto color theme
window attribute manipulation
Syntax
WINDOW LEVEL [<levels>]
WINDOW IMMORTAL on|off|toggle
WINDOW SERVER [-sticky | -unsticky] <tag>
WINDOW NUMBER [-sticky] <number>
WINDOW NAME <name>
WINDOW HISTORY [-clear] <name>
WINDOW THEME [-delete] [<name>]
WINDOW STICK [<ref#>] [ON|OFF]
WINDOW HIDELEVEL [<levels>]
Parameters
LEVEL:
Changes the text levels to display in the window, or query the current level.
IMMORTAL:
Modifies or queries the window mortality status. Immortal windows have an extra protection against WINDOW CLOSE.
SERVER:
Change the active server of the window or the server stickyness. If the server is sticky, it cannot be cycled with next_window_item/previous_window_item
NUMBER:
Change the active window number to the specified number, swapping the window already in that place if required. With -sticky, protect the window number from renumbering done by windows_auto_renumber. (To re-set the sticky attribute, use WINDOW NUMBER again without -sticky.)
NAME:
Change or clear the window name. Window names must be unique.
HISTORY:
Set or clear a specific named history to use for this window. All windows with the same named history will share a history.
THEME:
Applies or removes a per-window theme.
STICK:
Make the currently active window sticky, or stick the window specified by number to the currently visible split window. Or turn off stickyness of the currently active window or the window specified by number.
HIDELEVEL:
Changes the levels of text lines that should be hidden from view, or query the current hidden level.
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
/WINDOW LEVEL -ALL +NOTICES
/WINDOW HIDELEVEL ^JOINS ^PARTS ^QUITS
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel
window items
Syntax
WINDOW ITEM PREV
WINDOW ITEM NEXT
WINDOW ITEM GOTO <number>|<name>
WINDOW ITEM MOVE <number>|<name>
Parameters
ITEM PREV:
Make the previous item in this window active.
ITEM NEXT:
Make the next item in this window active.
ITEM GOTO:
Change to the query with the specified nickname, channel with the given name or window item number.
ITEM MOVE:
Move the active window item to another window, or move the channel or query item specified by their name to the current window.
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
/WINDOW ITEM MOVE 10
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel
window moving/number changing
Syntax
WINDOW NUMBER [-sticky] <number>
WINDOW MOVE PREV
WINDOW MOVE NEXT
WINDOW MOVE FIRST
WINDOW MOVE LAST
WINDOW MOVE <number>|<direction>
WINDOW MOVE LEFT [-directional]
WINDOW MOVE RIGHT [-directional]
Parameters
NUMBER:
Change the active window number to the specified number, swapping the window already in that place if required. With -sticky, protect the window number from renumbering done by windows_auto_renumber. (To re-set the sticky attribute, use WINDOW NUMBER again without -sticky.)
MOVE PREV:
Move the window to the place of the numerically previous window. At the first position, move the window to the end and renumber the consecutive block that it was part of.
MOVE NEXT:
Move the window to the place of the numerically next window. At the last position, move the window to the first position and renumber the consecutive block at first position (if any)
MOVE FIRST:
Move the window to the first position. Any windows inbetween are moved to their numerically next positions.
MOVE LAST:
Move the window to the last position. Any windows inbetween are moved to their numerically previous positions.
MOVE:
Move the window to the specified number or the first number that is in use when moving the window in the direction of the specified position. Any windows inbetween are shifted towards the old position of the window (unused positions remain empty)
MOVE LEFT:
Move the window to the numerically previous location inside the current sticky group.
MOVE RIGHT:
Move the window to the numerically next location inside the current sticky group.
-directional:
Set the split window in the given direction to the current one active, or move the current window to the sticky group of the split window in the given direction. (If no sticky group remains, the split window collapses.)
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel


window: split window resizing
Syntax
WINDOW GROW [-right] [<lines>|<columns>]
WINDOW SHRINK [-right] [<lines>|<columns>]
WINDOW SIZE [-right] <lines>|<columns>
WINDOW BALANCE [-right]
Parameters
GROW:
Increase the size of the active split window by the specified number of lines.
SHRINK:
Decrease the size of the active split window by the specified number of lines.
SIZE:
Set the current split window size to the specified number of lines.
BALANCE:
Balance the heights of all split windows.
-right:
Makes the command work on the width instead of height, or create the split window to the right instead of top.
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel

Page source
Toggle Light / Dark / Auto color theme
window: split window navigation
Syntax
WINDOW UP [-directional]
WINDOW DOWN [-directional]
WINDOW LEFT [-directional]
WINDOW RIGHT [-directional]
Parameters
UP:
Set the split window left or above the current one active. At the top, wraps to the bottom.
DOWN:
Set the split window right or below the current one active. At the bottom, wraps left.
LEFT:
Go to the previous window numerically that is part of the current sticky group (or not part of any sticky group).
RIGHT:
Go to the next window numerically that is part of the current sticky group (or not part of any sticky group).
-directional:
Set the split window in the given direction to the current one active, or move the current window to the sticky group of the split window in the given direction. (If no sticky group remains, the split window collapses.)
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel
window: split window moving
Syntax
WINDOW STICK [<ref#>] [ON|OFF]
WINDOW MOVE LEFT [-directional]
WINDOW MOVE RIGHT [-directional]
WINDOW MOVE UP [-directional]
WINDOW MOVE DOWN [-directional]
Parameters
STICK:
Make the currently active window sticky, or stick the window specified by number to the currently visible split window. Or turn off stickyness of the currently active window or the window specified by number.
MOVE LEFT:
Move the window to the numerically previous location inside the current sticky group.
MOVE RIGHT:
Move the window to the numerically next location inside the current sticky group.
MOVE UP:
Move the current window to the sticky group of the previous split window. If no sticky group remains, the split window collapses.
MOVE DOWN:
Move the current window to the sticky group of the next split window. If no sticky group remains, the split window collapses.
-directional:
Set the split window in the given direction to the current one active, or move the current window to the sticky group of the split window in the given direction. (If no sticky group remains, the split window collapses.)
Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with + are added to the current levels. Levels listed starting with - are removed from the current levels. To clear the levels, start the new level setting with NONE. Levels listed starting with ^ are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

Description
Manipulates the window layout and positioning attributes.

Examples
See also
JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel



statusbar
Syntax
STATUSBAR ADD|MODIFY [-disable | -nodisable] [-type window|root] [-placement top|bottom] [-position #] [-visible always|active|inactive] <statusbar>
STATUSBAR RESET <statusbar>
STATUSBAR ADDITEM|MODIFYITEM [-before | -after <item>] [-priority #] [-alignment left|right] <item> <statusbar>
STATUSBAR REMOVEITEM <item> <statusbar>
STATUSBAR INFO <statusbar>
Parameters
ADD:
Adds a statusbar to the list of statusbars.
MODIFY:
Modifies the configuration of a statusbar.
RESET:
Restores the default statusbar configuration.
ADDITEM:
Adds an item to the specified statusbar. It can be set to appear before/after another item and left/right aligned on the screen.
MODIFYITEM:
Changes an item position inside a bar.
REMOVEITEM:
Removes an item from the specified statusbar.
INFO:
List the current details and items of the specified statusbar.
-disable:
Removes a statusbar from the list.
-type:
Sets the type of statusbar, for each split window or only once at the root (very top or bottom) of the screen.
-placement:
Sets the placement of the statusbar, either at the top or the bottom of the screen or split window.
-position:
Sets the position of the statusbar. Represented as a number, with smaller numbers implying a position further to the top.
-visible:
Sets the visibility of the statusbar. If set to always, it is visible on all split windows, otherwise if set to inactive or active then it is only visible on inactive or active split windows, respectively.
-before:
This item is added before the other item.
-after:
This item is added after the other item.
-priority:
When the statusbar items overflow, the item with the lowest priority is removed or truncated first. Priority can be negative, in which case it’ll have to be quoted (e.g. -priority “-1”)
-alignment:
Display the item on the right side.
Where statusbar refers to the name of the statusbar; if no argument is given, or LIST is given, the entire list of statusbars along with a quick overview of their properties will be displayed.

Description
Allows adjustment of the attributes and items of a statusbar, as well as where it is located and whether or not it is currently visible.

Examples
/STATUSBAR
/STATUSBAR INFO window
/STATUSBAR REMOVEITEM time window
/STATUSBAR ADDITEM time window
/STATUSBAR RESET window
/STATUSBAR MODIFY -disable topic
/STATUSBAR MODIFY -nodisable topic
Remarks
Statusbar syntax was changed in Irssi 1.2. The old syntax is still accepted for backward compatibility, but no longer documented.

See also





