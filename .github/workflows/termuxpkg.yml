on:
  push:
    branches:
     - master
  pull_request:
name: Build Irssi Termux package
jobs:
  termux-package:
    runs-on: ubuntu-latest
    steps:
      - name: checkout termux-packages
        uses: actions/checkout@main
        with:
          repository: termux/termux-packages
      - name: checkout irssi
        uses: actions/checkout@main
        with:
          path: src.irssi.git
      - name: download termux docker container
        uses: docker://termux/package-builder:latest
      - name: create irssi build receipe
        run: |
          mkdir packages/irssi-an
          cat << 'BUILD_SH' > packages/irssi-an/build.sh
          TERMUX_PKG_HOMEPAGE=https://irssi.org/
          TERMUX_PKG_DESCRIPTION="Terminal based IRC client"
          TERMUX_PKG_LICENSE="GPL-2.0"
          TERMUX_PKG_MAINTAINER="@irssi"
          TERMUX_PKG_VERSION=@VERSION@
          TERMUX_PKG_REVISION=@REVISION@
          TERMUX_PKG_SRCURL=git+file:///home/<USER>/termux-packages/src.irssi.git
          TERMUX_PKG_AUTO_UPDATE=true
          TERMUX_PKG_DEPENDS="glib, libandroid-glob, libiconv, libotr, ncurses, openssl, perl, utf8proc"
          TERMUX_PKG_BREAKS="irssi"
          TERMUX_PKG_REPLACES="irssi"
          TERMUX_MESON_PERL_CROSS_FILE=$TERMUX_PKG_TMPDIR/meson-perl-cross-$TERMUX_ARCH.txt
          TERMUX_PKG_EXTRA_CONFIGURE_ARGS="
          -Dfhs-prefix=$TERMUX_PREFIX
          --cross-file $TERMUX_MESON_PERL_CROSS_FILE
          "

          termux_step_pre_configure() {
              LDFLAGS+=" -landroid-glob"

              # Make build log less noisy.
              CFLAGS+=" -Wno-compound-token-split-by-macro"

              local perl_version=$(. $TERMUX_SCRIPTDIR/packages/perl/build.sh; echo $TERMUX_PKG_VERSION)

              cat << MESON_PERL_CROSS >$TERMUX_MESON_PERL_CROSS_FILE
          [properties]
          perl_version = '$perl_version'
          perl_ccopts = ['-I$TERMUX_PREFIX/include', '-D_LARGEFILE_SOURCE', '-D_FILE_OFFSET_BITS=64', '-I$TERMUX_PREFIX/lib/perl5/$perl_version/${TERMUX_ARCH}-android/CORE']
          perl_ldopts = ['-Wl,-E', '-I$TERMUX_PREFIX/include', '-L$TERMUX_PREFIX/lib/perl5/$perl_version/${TERMUX_ARCH}-android/CORE', '-lperl', '-lm', '-ldl']
          perl_archname = '${TERMUX_ARCH}-android'
          perl_installsitearch = '$TERMUX_PREFIX/lib/perl5/site_perl/$perl_version/${TERMUX_ARCH}-android'
          perl_installvendorarch = ''
          perl_inc = ['$TERMUX_PREFIX/lib/perl5/site_perl/$perl_version/${TERMUX_ARCH}-android', '$TERMUX_PREFIX/lib/perl5/site_perl/$perl_version', '$TERMUX_PREFIX/lib/perl5/$perl_version/${TERMUX_ARCH}-android', '$TERMUX_PREFIX/lib/perl5/$perl_version']
          MESON_PERL_CROSS
          }

          BUILD_SH
          version=$(awk '/^v/ { $0=$1; gsub(/^v/,""); gsub(/-head/,"dev"); gsub(/-/,""); print; exit }' src.irssi.git/NEWS)
          version=$version+g$(git -C src.irssi.git rev-parse --short HEAD)
          sed -i \
              -e "s:@VERSION@:$version:" \
              -e "s:@REVISION@:$GITHUB_RUN_NUMBER:" \
              packages/irssi-an/build.sh
          git -C src.irssi.git tag v$version
      - name: prepare output folder
        run: |
          install -m a+rwx -d output
      - name: build irssi package
        run: |
          sudo ./scripts/run-docker.sh ./build-package.sh -I irssi-an
      - uses: actions/upload-artifact@v4
        with:
          name: irssi-termux-pkg
          path: output/irssi-an*.deb
