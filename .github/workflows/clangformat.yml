on: [pull_request]
name: clang-format
jobs:
  check-clang-format:
    runs-on: ubuntu-22.04
    steps:
      - name: install clang-format
        run: sudo apt install clang-format-14
      - uses: actions/checkout@main
      - name: fetch target ref
        run:
          |
            refs=($(git log -1 --format=%s))
            git fetch --depth=1 origin "${refs[3]}"
      - name: configure clang-format
        run:
          |
            git config clangformat.binary $PWD/utils/clang-format-xs/clang-format-xs
            git config clangformat.extensions c,h,xs
      - name: run git-clang-format and Check if no changes are needed
        run:
          |
            CLANG_FORMAT=clang-format-14 git-clang-format-14 --diff FETCH_HEAD HEAD | tee git-clang-format.diff
            cmp -s <(echo no modified files to format) git-clang-format.diff || cmp -s <(echo -n) git-clang-format.diff
      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: git-clang-format.diff
          path: git-clang-format.diff
