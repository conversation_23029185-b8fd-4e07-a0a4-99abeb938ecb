on:
  push:
    branches:
     - master
  pull_request:
name: Check Irssi
env:
  apt_build_deps: ninja-build libutf8proc-dev libperl-dev libotr5-dev libglib2.0-dev
  get_pip_build_deps_meson: pip3 install setuptools${setuptools_ver}; pip3 install wheel; pip3 install meson${meson_ver}
  build_options_meson: -Dwith-proxy=yes -Dwith-bot=yes -Dwith-perl=yes -Dwith-otr=yes
  prefix: ~/irssi-build
jobs:
  dist:
    runs-on: ubuntu-latest
    env:
      meson_ver: <0.63.0
      setuptools_ver: <66
    steps:
      - name: prepare required software
        run: |
          sudo apt update && sudo apt install $apt_build_deps
          eval "$get_pip_build_deps_meson"
          patch ~/.local/lib/python3.12/site-packages/pkg_resources/__init__.py <<- PATCH
          --- __init__.py       2024-12-16 20:37:46.733230351 +0100
          +++ __init__.py       2024-12-16 20:38:42.479554540 +0100
          @@ -2188,7 +2188,8 @@ def resolve_egg_link(path):
               return next(dist_groups, ())
           
           
          -register_finder(pkgutil.ImpImporter, find_on_path)
          +if hasattr(pkgutil, 'ImpImporter'):
          +    register_finder(pkgutil.ImpImporter, find_on_path)
           
           if hasattr(importlib_machinery, 'FileFinder'):
               register_finder(importlib_machinery.FileFinder, find_on_path)
          @@ -2345,7 +2346,8 @@ def file_ns_handler(importer, path_item,
                   return subpath
           
           
          -register_namespace_handler(pkgutil.ImpImporter, file_ns_handler)
          +if hasattr(pkgutil, 'ImpImporter'):
          +    register_namespace_handler(pkgutil.ImpImporter, file_ns_handler)
           register_namespace_handler(zipimport.zipimporter, file_ns_handler)
           
           if hasattr(importlib_machinery, 'FileFinder'):
          PATCH
      - uses: actions/checkout@main
      - name: make dist
        run: |
          ./utils/make-dist.sh
      - uses: actions/upload-artifact@v4
        with:
          path: irssi-*.tar.gz
          retention-days: 1
  install:
    runs-on: ${{ matrix.os }}
    env:
      CC: ${{ matrix.compiler }}
    needs: dist
    continue-on-error: ${{ contains(matrix.flags, 'FAILURE-OK') }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04, ubuntu-latest]
        compiler: [clang, gcc]
        flags: [regular]
        setuptools_ver: [<66]
        include:
          - os: ubuntu-22.04
            meson_ver: ==0.53.2
            setuptools_ver: <51
          - os: ubuntu-latest
            meson_ver: <0.63.0
          - os: ubuntu-latest
            flags: meson-latest FAILURE-OK
    steps:
      - name: fetch dist
        uses: actions/download-artifact@v4
      - name: set PATH
        run: |
          echo "$HOME/.local/bin" >> $GITHUB_PATH
      - name: prepare required software
        env:
          meson_ver: ${{ matrix.meson_ver }}
          setuptools_ver: ${{ matrix.setuptools_ver }}
        run: |
          sudo apt update && sudo apt install $apt_build_deps
          eval "$get_pip_build_deps_meson"
          curl -SLf https://github.com/irssi-import/actions-irssi/raw/master/check-irssi/render.pl -o ~/render.pl && chmod +x ~/render.pl
      - name: unpack archive
        run: tar xaf artifact/irssi-*.tar.gz
      - name: build and install with meson
        run: |
          # ninja install
          cd irssi-*/
          meson Build $build_options_meson --prefix=${prefix/\~/~}
          ninja -C Build
          ninja -C Build install
      - name: run tests with Meson
        run: |
          # ninja test
          cd irssi-*/
          ninja -C Build test
          find -name testlog.txt -exec sed -i -e '/Inherited environment:.* GITHUB/d' {} + -exec cat {} +
      - name: run launch test
        env:
          TERM: xterm
        run: |
          # automated irssi launch test
          cd
          mkdir irssi-test
          echo 'echo automated irssi launch test
          ^set settings_autosave off
          ^set -clear log_close_string
          ^set -clear log_day_changed
          ^set -clear log_open_string
          ^set log_timestamp * 
          ^window log on
          load irc
          load dcc
          load flood
          load notifylist
          load perl
          load otr
          load proxy
          ^quit' > irssi-test/startup
          irssi-build/bin/irssi --home irssi-test | perl -Mutf8 -C ~/render.pl
          cat irc.log.*
  annotation-warnings:
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'pull_request' }}
    env:
      CC: clang
    steps:
      - name: prepare required software
        run: |
          sudo apt update && sudo apt install $apt_build_deps
      - uses: actions/checkout@main
      - name: Setup local annotations
        uses: irssi-import/actions-irssi/problem-matchers@master
      - name: set PATH
        run: |
          echo "$HOME/.local/bin" >> $GITHUB_PATH
      - name: prepare required software
        env:
          meson_ver: ${{ matrix.meson_ver }}
          setuptools_ver: ${{ matrix.setuptools_ver }}
        run: |
          sudo apt update && sudo apt install $apt_build_deps
          eval "$get_pip_build_deps_meson"
          curl -SLf https://github.com/irssi-import/actions-irssi/raw/master/check-irssi/render.pl -o ~/render.pl && chmod +x ~/render.pl
      - name: build and install with meson
        run: |
          meson Build $build_options_meson --prefix=${prefix/\~/~}
          ninja -C Build
          ninja -C Build install >/dev/null
      - name: run launch test
        env:
          TERM: xterm
        run: |
          # automated irssi launch test
          cd
          mkdir irssi-test
          echo 'echo automated irssi launch test
          ^set settings_autosave off
          ^set -clear log_close_string
          ^set -clear log_day_changed
          ^set -clear log_open_string
          ^set log_timestamp * 
          ^window log on
          load irc
          load dcc
          load flood
          load notifylist
          load perl
          load otr
          load proxy
          ^quit' > irssi-test/startup
          irssi-build/bin/irssi --home irssi-test | perl -Mutf8 -C ~/render.pl
          cat irc.log.*
