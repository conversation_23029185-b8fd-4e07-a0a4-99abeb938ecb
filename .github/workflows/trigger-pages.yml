on:
  push:
    branches:
      - master

jobs:
  rebuild-pages:
    if: github.repository == 'irssi/irssi'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.PAT_TOKEN }}
          script: |
            await github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: 'irssi.github.io',
              workflow_id: 'pages.yml',
              ref: 'main'
            })
