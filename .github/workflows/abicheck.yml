on: [pull_request]
name: abicheck
env:
  build_options: -Dbuildtype=debug -Denable-true-color=yes -Dwith-proxy=yes -Dc_args=-DPERL_EUPXS_ALWAYS_EXPORT
  prefix: /usr/local
  apt_build_deps: ninja-build libutf8proc-dev libperl-dev libotr5-dev libglib2.0-dev
  get_pip_build_deps: pip3 install 'setuptools<66'; pip3 install wheel; pip3 install 'meson<0.59.0'
  getabidef_def: getabidef() { awk '$1=="#define" && $2=="IRSSI_ABI_VERSION" { print $3 }' "$1"/include/irssi/src/common.h; }
jobs:
  build-base-ref:
    runs-on: ubuntu-latest
    outputs:
      base_abi: ${{ steps.out.outputs.base_abi }}
    steps:
      - name: set PATH
        run: |
          echo "$HOME/.local/bin" >> $GITHUB_PATH
      - name: prepare required software
        run: |
          sudo apt install $apt_build_deps
          eval "$get_pip_build_deps"
      - name: checkout base ref
        uses: actions/checkout@main
        with:
          path: base.src
          ref: ${{ github.base_ref }}
      - name: build base ref
        run: |
          meson Build.base base.src $build_options
          ninja -C Build.base
          DESTDIR=$PWD/base ninja -C Build.base install
      - id: out
        run: |
          # print versions and abi versions
          eval "$getabidef_def"
          base_abi=$(getabidef base$prefix)
          echo base abi : $base_abi
          ./base$prefix/bin/irssi --version
          echo base_abi=$base_abi >> $GITHUB_OUTPUT
      - uses: actions/upload-artifact@v4
        with:
          name: base.inst
          path: base
          retention-days: 1
  build-merge-ref:
    runs-on: ubuntu-latest
    outputs:
      merge_abi: ${{ steps.out.outputs.merge_abi }}
    steps:
      - name: set PATH
        run: |
          echo "$HOME/.local/bin" >> $GITHUB_PATH
      - name: prepare required software
        run: |
          sudo apt install $apt_build_deps
          eval "$get_pip_build_deps"
      - name: checkout merge ref
        uses: actions/checkout@main
        with:
          path: merge.src
      - name: build merge ref
        run: |
          meson Build.merge merge.src $build_options
          ninja -C Build.merge
          DESTDIR=$PWD/merge ninja -C Build.merge install
      - id: out
        run: |
          # print versions and abi versions
          eval "$getabidef_def"
          merge_abi=$(getabidef merge$prefix)
          echo merge abi : $merge_abi
          ./merge$prefix/bin/irssi --version
          echo merge_abi=$merge_abi >> $GITHUB_OUTPUT
      - uses: actions/upload-artifact@v4
        with:
          name: merge.inst
          path: merge
          retention-days: 1
  check-abi-diff:
    runs-on: ubuntu-latest
    needs:
      - build-merge-ref
      - build-base-ref
    env:
      base_abi: ${{ needs.build-base-ref.outputs.base_abi }}
      merge_abi: ${{ needs.build-merge-ref.outputs.merge_abi }}
    steps:
      - name: prepare required software
        run: |
          sudo apt install abigail-tools
      - name: fetch base build
        uses: actions/download-artifact@v4
        with:
          name: base.inst
          path: base
      - name: fetch merge build
        uses: actions/download-artifact@v4
        with:
          name: merge.inst
          path: merge
      - run: |
          # abipkgdiff
          abipkgdiff -l base merge >abipkgdiff.out && diff_ret=0 || diff_ret=$?
          echo "diff_ret=$diff_ret" >> $GITHUB_ENV
          cat abipkgdiff.out
      - uses: actions/upload-artifact@v4
        with:
          path: abipkgdiff.out
      - run: |
          # Check if no changes are needed
          if [ "$diff_ret" -ne 0 ]; then
              if [ "$base_abi" -lt "$merge_abi" ]; then
                  echo "::warning ::abigail found changes and ABI changed from $base_abi to $merge_abi"
                  exit 0
              else
                  echo "::error ::Looks like the ABI changed but the IRSSI_ABI_VERSION did not"
                  exit $diff_ret
              fi
          else
              if [ "$base_abi" -ne "$merge_abi" ]; then
                  echo "::error ::abigail found no changes yet the IRSSI_ABI_VERSION changed. Is this correct?"
                  exit 1
              else
                  : "No changes detected and IRSSI_ABI_VERSION untouched"
                  exit 0
              fi
          fi
