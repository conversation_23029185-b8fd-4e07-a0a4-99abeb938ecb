v1.5-head 202x-xx-xx  The Irssi team <<EMAIL>>

v1.4.5 2023-10-03  The Irssi team <<EMAIL>>
	+ Add workaround for Perl 5.38.0 bug that breaks the Irssi
          locale and glyph rendering (scripts.irssi.org#857, #1498)
	- Fix Perl scripts broken by Perl 5.38 (scripts.irssi.org#851,
          #1474). With input from <PERSON>
	- Document workaround to fix linker errors when building with
          meson on Apple (#1435, #1492)
	- Fix meson using wrong filenames on Apple, leading to broken
          Perl support (#1483, #1493)
	- Fix /upgrade not accepting `~' (#1460, #1462). By Lukas Mai 
	- Improve compatibility with Perl5-IDEA (#1465, #1467). By
          <PERSON>
	- Fix logic in how own actions are printed for other protocols
          (codeberg!5, #1471). By <PERSON><PERSON>
	- Fix crash on old PowerPC Mac (#1482, #1484)
	- Fix wrong prototype of library function used in terminal
          handling (#1495). By <PERSON>
	- Minor cleanups (#1488, #1497). Includes work by <PERSON>
	- Minor help and documentation fixes (#1458, #1494, #1477,
          #1478). Includes work by KindOne

v1.4.4 2023-03-31  The Irssi team <<EMAIL>>
	* Expose location of signals.txt via pkg-config (codeberg!1,
          #1439, #1446, #1447). By Andrej Kacian
	+ Levels can now be inverted using `^' prefix in the /WINDOW
	  (HIDE)LEVEL commands (#1428)
	+ Support receiving monospace (#1370, #1442). By Linus
	  Heckemann
	- Fix config broken by /WINDOW LOGFILE without parameter
          (#1392, #1432)
	- Fix broken /LASTLOG -window switch (#1418, #1424, regression
          introduced in #1398)
	- Fix logic in how actions are printed for other protocols
          (codeberg!3, #1454). By Andrej Kacian
	- Fix stale special collector use after free (#1456). Reported
          by ednash
	- Help and message fixes (#1421, #1429, #1431, #1434, #1438,
          #1452, #1453, #1455). Includes work by Jimmie Tyrrell and
          Sven Grewe

v1.4.3 2022-10-31  The Irssi team <<EMAIL>>
	- Fix freeze on Alt+arrows (#1416, #1417)
	- Fix crash on /upgrade (#1399, #1419)
	- Fix saving of -disallow_starttls (#1401, #1420)
	- Fix libnv detection on FreeBSD (freebsd#265397, #1407). By
	  Guido Falsi and Kristof Provost
	- Minor help and script fixes (#1380, #1393, #1411, #1413,
	  #1414, #1415)

v1.4.2 2022-07-17  The Irssi team <<EMAIL>>
	* Add irssimoduledir to irssi-1.pc in the Meson build (#1383,
          #1378)
	- Use -isystem includes, limiting warnings to Irssi
          code. Fixes compilation with Perl 5.36 (#1381,
          gentoo#851522, #1384)
	- Fix missing lines when changing dynamic textbuffer contents
          (#1382, #1387). Reported by externalserver15
	- Fix Perl cross compilation with Meson 0.60.0 (#1377)
	- Fix default channel prefix used for /JOIN to default to `#'
          (#1385, #1388)
	- Fix crash in /LASTLOG by getting line texts before printing
          (#1367, #1398)

v1.4.1 2022-06-12  The Irssi team <<EMAIL>>
	! Note: botti no longer compiles; get in touch if you use it
	* Format the output of /QUOTE HELP (#1371, an#82). By Val
	  Lorentz. Add /SHELP as default alias (an#83)
	+ GLib log message filter: /SET glib_log_domains (an#50,
	  an#59). By Andrej Kacian
	+ An option to clear the cutbuffer:
	  /SET empty_kill_clears_cutbuffer (an#58). By Mikael
	  Magnusson
	+ Scriptable pastebin (an#60, an#88)
	+ Configurable actlist separator: /SET actlist_separator
	  (#1364, an#61)
	- Fix window left/right not skipping visible windows
	  (an#57). By Mikael Magnusson
	- Fix wrong printf-format on OpenBSD (an#66, an#68). Reported
	  by Aaron Bieber
	- Fix erroneous output produced by autoload_modules (an#72)
	- Fix scroll_page_count setting with `.' (#1365, an#76)
	- Fix memory leak in /IGNORE (#1373, an#84). Found by Jookia
	- Misc fixes (an#45, an#67, an#70, #1368, an#77)

	- CHANTYPES take precedence over (missing) STATUSMSG in /join
          (#1358, an#54)
	- Fix crash in Perl's $view->set_bookmark (freebsd#254237,
          an#56)

	- Minor help fixes (an#51, an#52)
	- Fix regression where own channel status was forgotten after
          /UPGRADE (#1357, an#53)

	* /SET resolve_reverse_lookup setting was removed (#1034,
          #1135)
	* Irssi will try to connect on IPv4 if IPv6 connection failed
	  (#1146). By Shivaram Lingamneni

	* The display system now renders formats on the fly (#1079,
	  #1188, #1191, #1192, #1204, #1205, #1209, #1349, #1355,
	  an#13, an#14, an#28, an#29, an#36, an#37, an#49)

	  This major change will break scripts that try to modify
	  printed text during "print text" signal (#1189). They need
	  to be ported to modify the text during "print format"
	  instead. It also breaks the usage of using /FORMAT to add
	  different colours to a line. Such usage needs to be ported
	  to using $expando variables instead. Affected scripts
	  include format_identify.pl, friends_peder.pl, nickcolor.pl,
	  nm.pl, people.pl

	  The "gui print text finished" and "gui print text after
	  finished" signals gained a TEXT_DEST_REC *parameter in the
	  process.

	  A new "gui render line text" signal is available to change
	  the rendering of a line

	* made the $Z expando (time) dynamic (#1087, #1207, #1208)

	  This change breaks the usage of /SET timestamp_format to
	  supply a custom displayed time stamp. Affected scripts
	  include binary_time.pl

	* /HILIGHT -priority now affects which hilight rule gets
	  applied (#1228, #1232)
	* The NAMES list is now hidden by default if there are more
	  than 18 users on the channel (an#7)

	  To revert to the previous behaviour

	      /SET show_names_on_join_limit -1

	* -tls_verify is now enabled by default (#1170, an#18, #1309,
           an#23, #1343, #1351)

	  This may cause an ugly display of notls_verify in the output
	  of /SERVER LIST, even on plain-text connection, on old
	  configs. Kindly remove the "tls_verify = "no";" entries from
	  your config file manually.

	* Irssi will now attempt STARTTLS if advertised (#1170, #1312,
	  an#19)

	  Use -disallow_starttls if you absolutely do not want this

	  In order to check for a STARTTLS advertisement, Irssi will
	  now wait for a response (even an error) to CAP LS 302. If
	  your bouncer/server does not want to communicate before
	  receiving USER/PASS at all, use -nocap to disable the CAP
	  check.

	* Channel sync requests (WHO, MODE) are now sent "later" than
          user commands. This should improve responsiveness to user
          commands in the autojoin phase (#1300, an#26, an#32, an#33)
	* Irssi is now using full paths in #include directives and
          consequently does not add all directories to the include
          path anymore (#1040)
	* The Build System was ported to Meson (#1064, #1065, #1068,
	  #1071, #1072, #1073, #1074, #1075, #1084, #1085, #1118, #1166,
	  #1223, #1224, #1245, #1313, #1314, an#31)
	* Scriptassist was changed to use a YAML database (#1163)

	  It will tell you when you need to update your setting

	* /BIND shows all partial matches (#1155)
	* Cleanup of unused functions (#1017, #1132, #1145, #1182,
	  #1246, #1264)

	  Functions removed:

	      NET_CALLBACK
	      NET_HOST_CALLBACK
	      RESOLVED_NAME_REC
	      net_gethostbyaddr_nonblock
	      net_connect_nonblock
	      [ SIMPLE_THREAD_REC, simple_init, simple_readpipe ]
	      hash_save_key

	  Functions deprecated:

	      dec2octal
	      g_timeval_cmp
	      get_timeval_diff

	  Function names corrected:

	      g_input -> i_input
	      g_istr -> i_istr
	      g_io_channel -> i_io_channel
	      g_hash_free_value -> i_hash_free_value
	      remove g_free_true
	      gslist -> i_slist
	      glog_func -> i_log_func
	      glist -> i_list

	  If multi-version compatibility is desired, module authors
	  can find an example of backwards compatible code in
	  cdidier/irssi-xmpp#55

	+ Add MSGLEVEL_HIDDEN to Perl (#1044)
	+ Add $view->set_hidden_level and $view->remove_lines_by_level
	  to Perl (#1026)
	+ Add a /SET scrollback_max_age setting (#1022). By Heikki
	  Orsila
	+ Add /SET actlist_prefer_window_name (#1025)
	+ Add -window option to /CAT (#1023, #1159)
	+ Add an option to list specific sections with

	      /SET -section lookandfeel

	  (#1048)

	+ Add support for IRCv3 CAP LS 302 (#1091)
	+ Add a new "print noformat" signal that goes together with
	  "print format" (#1088, #1192)
	+ Add support for IRCv3 extended-join. /SET show_extended_join
	  to enable (#1097, #1107, #1124)

	  There are two new /FORMATs, join_extended and
	  join_extended_account, that theme writers need to take into
	  account if desired.

	+ Add support for IRCv3 setname (#1093, #1104, #1254, GL#33)
	+ Add support for IRCv3 account-notify (#1100, #1098, GL#33,
	  #1105, #1131). Credit to oss-fuzz
	  /SET show_account_notify to enable
	+ Add support for IRCv3 invite-notify (#1094)
	+ Add support for receiving IRCv3 message-tags (#576, #1090)
	+ Add support for sending IRCv3 message-tags (#1092, an#34)
	+ Enable the znc.in/self-message CAP by default (#1123)
	+ Add support for IRCv3 away-notify. /SET away_notify_public
	  to enable (#1099, GL#33, #1105)
	+ Add support for IRCv3 chghost (#1096, GL#33, #1105)

	  For servers with broken chghost implementation that fill the
	  status window with host changed messages, one may add "quote
	  cap req -chghost" to the -autosendcmd or, if the host change
	  messages are entirely undesired, "/format -delete
	  host_changed"

	+ Add support for IRCv3 server-time. /SET show_server_time to
	  enable (#1108)
	+ Add support for logging IRCv3 server-time.
	  /SET log_server_time to disable (#1318, an#16)
	+ Add IRCv3 features to signals.txt (#1111)

	  In particular, "message join" now takes 2 additional
	  arguments, script and module authors must beware of this
	  change.

	+ Show the unignore time in /IGNORE output (#1158, #1161)
	+ Add /SET quit_on_hup to make the behaviour of SIGHUP
	  configurable (#828, #1169). By Pinguin1234
	+ Support numeric 489 as ERR_SECUREONLYCHAN (#1193, #1196). By
	  Michael Hansen
	+ Improve support for building Irssi in Termux-Android with
	  Meson (#1199)
	+ Add usermode key to Irssi::Irc::Chatnet in Perl (#1288). By
	  Jessica Sophie Porter
	+ Add format_string_expand and format_string_unexpand
	  functions to Perl (#1286)
	+ Add ...->format_create_dest(...)->printformat("format",
	  args...) and ...->printformat_module("module", "format",
	  args...) methods to Perl (#1284)

	  You can avoid any CORE::GLOBAL::caller hacks using the
	  printformat_module method, especially sind that hack was not
	  safe during signal emissions

	+ Add tracking of user accounts in the channel nicklist using
	  WHOX on join (#1250)
	+ Add auto-loading of the Perl and otr module from /SET
	  autoload_modules (#1295)
	+ Add /IGNORE ... NOHILIGHT to ignore some hilights (#1260)
	+ Do not beep on hidden lines with /SET beep_msg_level
	  ... -HIDDEN (#1259)
	+ Added /CS, /MS, /NS, and /OS aliases to the default config
	  (#1316). By Mathis Beer
	+ Allow -tls_ca{file,path} '' to unset an argument (#730,
	  #1060, an#30)
	+ Add a "server outgoing modify" signal to intercept outgoing
	  messages (#1148, #1151, an#15, an#43). Original by
	  JustAnotherArchivist
	- remove some hard-coded 510 byte assumptions (#1086)
	- Several fixes for error checks in SSL (#944, #1037, #943,
          #1036). Reported by Chi Li
	- Wrong variable tested in mask_match (#902, #1035)
	- Fix bug where irssi-proxy with `?'-port would not reconnect
          (#1041)
	- Allow shrinking of /SET rawlog_lines (#957, #1020). By
          Marcus "Teschi" Prinz
	- Fix /WINDOW BALANCE warning (#1054)
	- fix overflow when first command history entry expires
          (#1070)
	- begin modularising IRC module (#1067, #1112, #1113)
	- fix some memory leaks in /DCC RESUME and settings_add
          (#1077). By Zero King
	- fix cut-off text with theme_indent module and /SET
          indent_always OFF (#1078)
	- fix the cap_queue order (#1095)
	- add reference counted strings (#1089)
	- Fix irc_op_public messages not triggering hilights (#354,
          #891, #1129). By Dan Collins
	- Fix /IGNORE not setting the right level in irc_op_public
          messages (#1280). Credit to oss-fuzz
	- Fix GTimeVal deprecation (#1141, #1144, #1145, #1350, an#44)

	  If multi-version compatibility is desired, module authors
	  can find an example of backwards compatible code in
	  cdidier/irssi-xmpp#53

	- Fix /IGNORE ... MODES NO_ACT not working (#1164)
	- Deprecated -ssl* options are hidden from tab completion
          (#1171)
	- Make /SET actlist_sort a choice type (#1198)
	- Fix crash from self-unloading script (#1206). By Thomas
          Stagner
	- Fix crash during Perl signal emission (#1233, #1234)
	- Fix a case where empty lines or comments inside channels or
          servers in the config would confuse Irssi (#1062, #1242,
          #1243)
	- Fix reported freezing in DCC GET on slow disks (#159, #1271)
	- Fix message-tags parsing (#1274, #1275). Credit to oss-fuzz
	- Fail redirects when receiving numeric 263 (RPL_TRYAGAIN) in
          response to /WHO (#1283)
	- Some updates to .gitignore (#1302). By Rene Kita
	- Fix build on operating systems with X/Open Curses, version 2
	  (#1305, #1308). By Nia Alarie (Regression introduced with
	  #1290, alternative fix for Irssi 1.2.3 no-term.h.patch)
	- Fix otr module not using g_strndup, e.g. on Solaris 10
          (#1315). By Claes Nästén
	- Fix cursor getting stuck for auto completions that changes
          case (#1176, #1322, an#8). By ffrogman
	- Restore operation of tag/* in /SET activity_hide_targets
	  (#1337, an#11) nb. the ::all syntax was working in Irssi 1.1
	  and 1.2 (and continues to work)
	- Fix /SERVER ADD -matrix -network my_matrix_network
          (an#12). By Andrej Kacian
	- Fix /SERVER ADD creating duplicated entries in the config
          file (#1317, an#22, an#41)
	- Fix critical when SASL user is set and SASL password is
          empty (#1325, an#21)
	- Misc fixes (#1106, #1141, #1272, #1297, an#35)
	- Fuzz fixes (#1116, #1117, #1119, #1125, #1126, an#20)
	- Build system fixes (#1101, #1102, #1069, #1140, #1181, #1253)
	- Sync docs and scripts (an#39)
	- Text and Help updates
	  - add -tls_* options to manual (#1029, #1030). By Jacob
            V. Rasmussen
	  - missing targets in /MSG (#1032)
	  - wrong parameter in /ECHO (#1024)
	  - Spelling in OTR (#1047). By David Gall
	  - Clarify statusbar priority (#1049). By Marius Gedminas
	  - Document get_irssi_dir in Perl (#1051, #1052). By Alex
            Shafer
	  - typo in /HILIGHT help (#1081). By DFrostByte
	  - improved clarity of your_nick_owned (#1138). By Mike Quin
	  - Update some URLs to https (#1163)
	  - Add documentation for escaping some characters (#1329,
            #1330, an#9). By Guntbert Reiter
	  - Fix some typos (#1336, an#10). By Francis Mteo
	  - Document $abiversion and parse_special (an#38). By bw1
	- Infrastructure updates:
	  - Support for Github Actions (#1039, #1103, #1160, #1212,
            #1231, #1252, #1261, an#40)
	  - Run clang-format on pull requests (#1172, #1173, #1184,
            #1230, #1247, #1287)
	  - Run abidiff on pull requests (#1179, #1195)
	  - Test CI-Fuzz (#1279, #1304, an#17)

v1.2.3 2021-04-11  The Irssi team <<EMAIL>>
	- Fix the compilation of utf8proc (#1021)
	- Fix wrong call to free. By Zero King (#1076)
	- Fix a colour reset in true colour themes when encountering
          mIRC colours (#1059)
	- Fix memory leak on malformed CAP requests (#1120)
	- Fix an erroneous free of SASL data. Credit to Oss-Fuzz (#1128,
	  #1130)
	- Re-set the TLS flag when reconnecting (#1027, #1134)
	- Fix the scrollback getting stuck after /clear (#1115, #1136)
	- Fix the input of Ctrl+C as the first character (#1153,  #1154)
	- Fix crash on quit during unloading of modules on certain
          platforms (#1167)
	- Fix Irssi freezing input after Ctrl+Space on GLib >2.62 (#1180,
	  #1183)
	- Fix layout of IDCHANs. By Lauri Tirkkonen (#1197)
	- Fix crash when server got reconnected before it was properly
          connected (#1210, #1211)
	- Fix multiple identical active caps (#1249)
	- Minor help corrections (#1156, #1213, #1214, #1255)
	- Remove erroneous colour in the colorless theme. Reported and
          fixed by Nutchanon Wetchasit (#1220, #1221)
	- Fix invalid bounds calculation when editing the text
          entry. Found and fixed by Sergey Valentey (#1269)
	- Fix passing of negative size in buffer writes. Found and
          fixed by Sergey Valentey (#1270)
	- Fix Irssi freezing on slow hardware and fast DCC transfers (#159,
	  #1271)
	- Fix compilation on Solaris (#1291)
	- Fix null pointer dereference when receiving broken JOIN
          record. Credit to Oss-Fuzz (#1292)
	- Fix crash on /connect to some sockets (#1239, #1298)
	- Fix Irssi rendering on Apple ARM. By Misty De Méo (#1267,
	  #1268, #1290)
	- Fix crash on /lastlog with broken lines (#1281, #1299)
	- Fix memory leak when receiving bogus SASL authentication
          data. Found and fixed by Sergey Valentey (#1293)

v1.2.2 2019-08-29  The Irssi team <<EMAIL>>
	- Fix a use after free issue when receiving IRCv3 CAP
          information from the server (GL#34, GL!35)
	- Fix a crash during startup when windows weren't fully
          initialised yet (#1114, bdo#935813)

v1.2.1 2019-06-29  The Irssi team <<EMAIL>>
	! Contains all changes from 1.1.3
	- Fix a test on big endian machines (#1014)
	- Fix the compile time conditionality of wcwidth
          implementation (#1019, gentoo#677804, #720)
	- Fix /save no longer working on old Solaris (pre
          POSIX.1-2008) (#1042, #1043)
	- Fix regression of #764 where display of 8-bit (legacy
          encoding) in the input prompt was broken (#1018,
          #1057). Initial patch by Артём Курашов

v1.1.3 2019-06-29  The Irssi team <<EMAIL>>
	! Contains all changes from 1.0.8
	- Fix regression of #779 where autolog_ignore_targets would
          not matching itemless windows anymore (#1012, #1013)

v1.0.8 2019-06-29  The Irssi team <<EMAIL>>
	- Fix a use after free issue when sending the SASL login on
          (automatic and manual) reconnects (#1055, #1058). Reported
          by ilbelkyr

v1.2.0 2019-02-11  The Irssi team <<EMAIL>>
	! Contains all changes from 1.1.2
	* Improved the /STATUSBAR commands (#858)
	* /SET no longer shows `=' between setting and value (#886)
	* /CUBES removed from default config (available as script)
          (#956)
	* /1 /2 /3 ... removed from default config (available as new
          setting window_number_commands) (#958)
	* Always redraw the screen on resize. By David Phillips (#896)
	* Private notices intended for channels are now displayed on
	  the channel (new setting notice_channel_context) (#959)
	+ Imported the "Off-the-record" module into Irssi tree (#854,
	  #589, #196, #881)
	+ Initial support for sideways split windows (#697, #431,
	  #224, #807, FS#310, #947, #955, #989)
	+ Change the implementation of `wcwidth'. This is used to
	  calculate the width of emojis on your terminal screen (#917,
	  #720)
	+ Make the wcwidth functions available from Perl (#973):

	      string_width(str)
	      string_chars_for_width(str, width)
	      wcwidth(char)

	+ Added completion_keep_word setting (#979)
	+ Allow activity_hide_targets to hide activity in itemless
	  windows (#967, #997, #1001, #1003)
	+ Added activity_hide_visible setting (#990)
	+ Allow hiding of lines through the /IGNORE system (#901,
	  #900, #892, #890, #884, #937)
	+ Add window_default_hidelevel setting. By Doug Freed (#941)
	+ Add activity_hide_window_hidelevel setting, defaulting to ON
	  (#938)
	+ Add autolog_only_saved_channels setting, to autolog only
	  channels that are in the config (#968)
	+ Add format support for the input line. By Ben Paxton,
	  originally by Jonas Hurrelmann (#764, FS#621, #1004)

	      use Irssi::TextUI;
	      gui_input_set_extent(pos, text)
	      gui_input_set_extents(pos, len, left, right)
	      gui_input_clear_extents(pos, len)
	      gui_input_get_extent(pos)
	      gui_input_get_text_and_extents()
	      gui_input_set_text_and_extents(...)

	+ Parsing of IRCv3 CAP 3.2 (#775, #869)
	+ Show CAP-related events in the user interface (#918, #916,
	  #870, #704)
	+ Continue using separators when addressing multiple nicks
	  with tab completion. By Manish Goregaokar (#822)
	+ Bind Shift-tab by default. By Niklas Luokkala (#830, #829)
	+ Fuzzing more things (#913, #780, #813)
	- Disconnect SASL properly in case the SASL module got
          unloaded from server (#931, #629, #618, #616)
	- Fix backward completion jumping to the first instead of last
          word (#979)
	- Improve empty topic handling (#961, #905, #911, #897, #888)
	- Prevent config truncation when no space left. By dequis and
          Lukas Waymann (#922, #925, #910, #909, #906, #871, #817)
	- Also time-out servers in lookup phase (#866, #130)
	- Fix build with LibreSSL 2.7. By Dorian Harmans (#865)
	- Fix a crash when appending to a textbuffer without
          line. Reported by Jari Matilainen (#862)
	- Fix segfault on sending large messages (#803, #796, #802)
	- Fix segfault on invalid statusbar config (#993, #994)
	- Fix random memory writes on restoring queries of foreign
          protocols (#999, #1000)
	- Make default keybinds deletable (#859, #507)
	- Fix freeze when resizing Irssi very small (#946)
	- Compare channels case-insensitively, avoiding confusions
          with the config file (#857, #856)
	- Fix DCC GET on Android. By Martin Staron (#844)
	- Improve rawlog performance (#957)
	- Fix nick escaping erroneously escaping quotes (#978, #974,
          #709)
	- Protect against theme recursion, improve padding
          performance, limit alignment padding. Credit to Oss-Fuzz
          (#835, #851, #850, #846, #848)
	- Fix recursive loop in replaces (#833, GL#23)
	- Fix headers for compilation of C modules (#939)
	- Documentation. By Zero King (#814). (#852)
	- Sync NEWS, docs, scripts (#849, #855)
	- Build system (#868, #867, #985, #988)
	- Fix build on IBM i and AIX. By Calvin Buckley (#975)
	- Misc fixes (#840, #839, #843, #953, #962). Tests (#806,
          #875, #905, #964, #1011). Fuzzing (#929).

v1.1.2 2019-01-09  The Irssi team <<EMAIL>>
	- Fix the resetting of window hiddenlevel (#861)
	- Fix clearing of hidelevel in layout (#951)
	- Fix accessing unallocated text when checking entry position
          (#928, #930)
	- Fix uninitialised memory on empty lines (#873, GL#31, #878,
          #877, #907, #914)
	- Fix use-after-free on expiration of hidden lines (#948)
	- Fix use-after-frees. By Maya Rashish (#919)
	- Fix out of bounds access in help display when window width
          is small (#949)
	- Fix paste_join_multiline (#970, #971)
	- Correctly check for errno when displaying SSL errors. By
          Janik Rabe (#895)
	- Fix wrong signal emission argument count (#965)
	- Documentation (#920). Sync NEWS, scripts (#849)
	- Fix Perl detection on MacOS. By Dominyk Tiller (#927)
	- Misc fixes. By Jaroslav Škarvada (#981, #982)

v1.1.1 2018-02-15  The Irssi team <<EMAIL>>
	! Contains all changes from 1.0.7
	- Restore compatibility with OpenSSL < 1.0.2 (#820, #831)
	- Fix test compilation on some platforms (#815, #816)
	- Fix portability and backwards compatibility of test runner
          (#818, #845)

v1.0.7 2018-02-15  The Irssi team <<EMAIL>>
	- Prevent use after free error during the execution of some
	  commands. Found by Joseph Bisch (GL#17, GL!24).
	- Revert netsplit print optimisation due to crashes (#465, #809,
          #812, #819, #824, #832).
	- Fix use after free when SASL messages are received in
          unexpected order (GL#26, GL!33).
	- Fix null pointer dereference in the tab completion when an
          empty nick is joined (GL#24, GL!31).
	- Fix use after free when entering oper password (GL#22,
          GL!32).
	- Fix null pointer dereference when too many windows are
          opened (GL#27, #837).
	- Fix out of bounds access in theme strings when the last
          escape is incomplete. Credit to Oss-Fuzz (#842).
	- Fix out of bounds write when using negative counts on window
          resize (GL#25, GL#29, #836).
	- Minor help correction. By William Jackson (#834).

v1.1.0 2018-01-15  The Irssi team <<EMAIL>>
	! Warning. Irssi is broken and will crash with OpenSSL < 1.0.2
          due to openssl/openssl commit
          5b4b9ce976fce09a7a92e2f25b91a1635cb840fe
	* Colour is now re-set when reaching a comma, matching mIRC
	  behaviour (#742, #740, #790)
	* Irssi now shows the initial nick and name on first start
	  (#785, #786)
	* lynx is no longer required to run autogen.sh (#81, #781)
	* The command history no longer permits wrapping around (#686)
	* /foreach now correctly sends arguments as commands, stopping
	  you from embarassing AMSGs (#659)
	* /server does not connect to servers anymore, use /server
	  connect to change servers (#559, #649).
	* The net_ip_compare API function is now deprecated, and the
          previously deprecated net_connect has been removed. By Will
          Storey (#770).
	+ Add an option to ignore all channels or ignore all queries
	  using /set activity_hide_targets. By Jari Matilainen (#612,
	  #779)
	+ Add a startup warning if the TERM var is wrong inside
	  tmux/screen (#726)
	+ Add option to hide certain levels from the textbuffer using
	  /window hidelevel (#746, #808)
	+ Irssi now has its first unit test (for mode parsing). By
	  Will Storey (#793)
	+ Added access to global command history when using window
	  history, and a binding to erase entries from the command
	  history (erase_history_entry) (#762)
	+ -alternate_nick is now available as a network specific
	  property. By Paul Townsend (#120, #771)
	+ On FreeBSD, Irssi now supports Capsicum sandbox (/capsicum
	  enter). By Edward Tomasz Napierala (#735, #755, #772)
	+ Filenames (directories) ending with a / now tab-complete
	  (#741)
	+ UTF-8 should now work in regular expressions when using
	  GRegex (the default) (#636, #653)
	+ Nicks are now properly escaped on completion. By Oscar
	  Linderholm (#693, #709)
	+ /server add -port <num> now works. By Jari Matilainen (#703)
	+ Add a setting key_timeout to make key sequences
	  automatically re-set when not finished (#644, #645)
	+ Warn users about expired client certificates, as servers may
	  refuse them (#211, #627)
	+ Add a new net_start_ssl function for StartTLS. This is
	  available from ABI 8 and can be used by protocol modules
	  (#615, #622).
	+ The %# code is now stored in the textbuffer, so for example
	  web scripts can make use of it (#626)
	+ Add new setting break_wide which can be used to enable
	  breaking of wide characters (for east-asian
	  users). Originally from FreeBSD ports. (#625)
	+ Add fuzzing code (#610, #620, #701, #713)
	- Netsplits show properly again (#812)
	- Do not error on blank lines when using /exec -o. By Fabian
	  Kurz (FS#902, #805)
	- Detect used nickname as reported by server. By Alexandre
	  Morignot (#219, #804)
	- Prevent use after free error during the execution of some
	  commands. Found by Joseph Bisch. (GL#17, GL!24)
	- Fix MODE parameter parsing when colon was used at a place
	  Irssi didn't expect (#601, #766)
	- Fixed code to compile with
	  -Werror=declaration-after-statement (#795)
	- Clang-format is now supported for git-clang-format (#784)
	- Fix use after free when changing the network of
	  hilights. Reported by Rui Mathias. (#787, #788)
	- Fix positioning error when tab-completing non-ascii
	  strings. (#752, #754)
	- In-development issues (#750, #751)
	- Clarify Alis in /help list (#699, #712)
	- Improve /lastlog performance from O(N^2) to O(N) (#715)
	- Fix a segfault on "script destroyed" signal. By Stephen
	  Oberholtzer (#660, #661).
	- Fix early ISON error (#596, #647)
	- Documentation improvements. By Paolo Martini (#639).
	  By Tristan Pepin (#731).  By Paul Townsend (#684, #736).
	  By Will Storey (#777)
	- Minor cleanups (#590). By Edward Tomasz Napierala (#734,
	  #738)
	- Fix space issue in glib-2.0.m4 (#621)

v1.0.6 2018-01-07  The Irssi team <<EMAIL>>
	! Note: Code and aliases using `$($'-like constructs are no
          longer supported due to issue GL#18. Sorry about the
          inconvenience.
	- Fix invalid memory access when reading hilight configuration
          (#787, #788).
	- Fix null pointer dereference when the channel topic is set
          without specifying a sender (GL#20, GL!25).
	- Fix return of random memory when using incomplete escape
          codes (GL#21, GL!26).
	- Fix heap buffer overflow when completing certain strings
          (GL#19, GL!27).
	- Fix return of random memory when using an incomplete
          variable argument (GL#18, GL!28).

v1.0.5 2017-10-23  The Irssi team <<EMAIL>>
	- Fix missing -sasl_method '' in /NETWORK (#718, #719).
	- Fix incorrect restoration of term state when hitting SUSP
          inside screen (#737, #733).
	- Fix out of bounds read when compressing colour
          sequences. Found by Hanno Böck (GL#12, GL!18).
	- Fix use after free condition during a race condition when
          waiting on channel sync during a rejoin (GL#13, GL!19).
	- Fix null pointer dereference when parsing certain malformed
          CTCP DCC messages (GL#14, GL!20).
	- Fix crash due to null pointer dereference when failing to
          split messages due to overlong nick or target (GL#15, GL!21).
	- Fix out of bounds read when trying to skip a safe channel ID
          without verifying that the ID is long enough (GL#16, GL!22).
	- Fix return of random memory when inet_ntop failed (#769).
	- Minor statusbar help update. By Robert Bisewski (#758,
          #763).

v1.0.4 2017-07-07  The Irssi team <<EMAIL>>
	- Fix null pointer dereference when parsing invalid timestamp (GL#10,
	  GL!15). Reported by Brian 'geeknik' Carpenter.
	- Fix use-after-free condition when removing nicks from the internal
	  nicklist (GL#11, GL!16). Reported by Brian 'geeknik' Carpenter.
	- Fix incorrect string comparison in DCC file names (#714).
	- Fix regression in Irssi 1.0.3 where it would claim "Invalid time '-1'"
	  (#716, #722).
	- Fix a bug when using \n to separate lines with expand_escapes (#723).
	- Retain screen output on improper exit, to better see any error
	  messages (#287, #721).
	- Minor help update (#729).

v1.0.3 2017-06-06  The Irssi team <<EMAIL>>
	! Regression info in 1.0.3: #716 Warnings on start up: invalid time '-1'
	- Fix out of bounds read when scanning expandos (GL!11).
	- Fix invalid memory access with quoted filenames in DCC
	  (GL#8, GL!12).
	- Fix null-pointer dereference on DCC without address (GL#9, GL!13).
	- Improve integer overflow handling. Originally reported by
          oss-fuzz#525 (#706).
	- Improve nicklist performance from O(N^2) to O(N) (#705).
	- Fix initial screen redraw delay. By Stephen Oberholtzer
	  (#680, bdo#856201).
	- Fix incorrect reset of true colours when resetting background. (#711).
	- Fix missing -notls option in /SERVER. By Jari Matilainen (#117, #702).
	- Fix minor history glitch on overcounter (#462, #685).
	- Improved OpenSSL detection at compile time. By Rodrigo Rebello (#677).
	- Improved NetBSD Terminfo detection. By Maya Rashish (#694, #698).
	- Add missing syntax info for COMPLETION (#687, #688).
	- Minor typo correction in help. By Michael Hansen (#707).

v1.0.2 2017-03-10  The Irssi team <<EMAIL>>
	! Warning. Irssi is broken on GLib 2.46 (bgo#755496)
	- Prevent some null-pointer crashes (GL!9).
	- Fix compilation with OpenSSL 1.1.0 (#628, #597).
	- Correct dereferencing of already freed server objects during
	  output of netjoins. Found by APic (GL!10, GL#7).
	- Fix in command arg parser to detect missing arguments in tail place
	  (#652, #651).
	- Fix regression that broke incoming DCC file transfers (#667, #656).
	- Fix issue with escaping \ in evaluated strings (#669, #520).

v1.0.1 2017-02-03  The Irssi team <<EMAIL>>
	- Fix Perl compilation in object dir. By Martijn Dekker (#602, #623).
	- Disable EC cryptography on Solaris to fix build (#604, #598).
	- Fix incorrect HELP SERVER example (#606, #519).
	- Correct memory leak in /OP and /VOICE. By Tim Konick (#608).
	- Fix regression that broke second level completion (#613, #609).
	- Correct missing NULL termination in perl_parse. By Hanno Böck (#619).
	- Sync broken mail.pl script (#624, #607).
	- Prevent a memory leak during the processing of the SASL
	  response (GL!8, GL#5)

v1.0.0 2017-01-03  The Irssi team <<EMAIL>>
	* Removed --disable-ipv6 (#408).
	* /connect Network now aborts with an error if no servers have been
	  added to that network (#443).
	* /dcc commands now use quotes around spaces consistently.
	* bell_beeps was removed (#524, #565).
	* Switch to GRegex instead of regex.h (#412).
	+ irssiproxy can now forward all tags through a single
	  port. By Lukas Mai (mauke, #425).
	+ irssiproxy can also listen on unix sockets. By Lukas Mai (#427).
	+ send channel -botcmds immediately when no mask is specified (#175, #399).
	+ the kill buffer now remembers consecutive kills.
	  New bindings were added: yank_next_cutbuffer and append_next_kill
	  By Todd A. Pratt (#353, #414, #455)
	+ connections will avoid looking up IPv6 addresses if the machine does
	  not have an IPv6 address assigned (exact behaviour is implementation
	  defined, #410).
	+ Fix potential crash if scripts insert undef values into the completion
	  list (#413).
	+ Paste warning is now also shown on pasting overlong
	  lines. By Manish Goregaokar (#426).
	+ autolog_ignore_targets and activity_hide_targets learn a new syntax
	      tag/* and * to ignore whole networks or everything.
	  By Jari Matilainen (vague666, #437)
	+ /hilight got a -matchcase flag to hilight case
	  sensitively. By Thibault B (isundil, #421, #476).
	+ Always build irssi with TLS support.
	+ Rename SSL to TLS in the code and add -tls_* versions of the -ssl_*
	  options to /CONNECT and /SERVER, but make sure the -ssl_* options continue
	  to work.
	+ Use TLS for Freenode, EFnet, EsperNet, OFTC, Rizon, and IRC6 in the default
	  configuration.
	+ Display TLS connection information upon connect. You can disable this by
	  setting tls_verbose_connect to FALSE.
	+ Add -tls_pinned_cert and -tls_pinned_pubkey for x509 and public key pinning.

	  The values needed for -tls_pinned_cert and -tls_pinned_pubkey is shown
	  when connecting to a TLS enabled IRC server, but you can also find the
	  values like this: Start by downloading the certificate from a given IRC
	  server:

	      $ openssl s_client -connect irc.example.net:6697 < /dev/null 2>/dev/null | \
	        openssl x509 > example.cert

	  Find the value for -tls_pinned_cert:

	      $ openssl x509 -in example.cert -fingerprint -sha256 -noout

	  Find the value for -tls_pinned_pubkey:

	      $ openssl x509 -in example.cert -pubkey -noout | \
	        openssl pkey -pubin -outform der | \
	        openssl dgst -sha256 -c | \
	        tr a-z A-Z

	+ Remove support for DANE validation of TLS certificates.

	  There wasn't enough support in the IRC community to push for this on the
	  majority of bigger IRC networks. If you believe this should be
	  reintroduced into irssi, then please come up with an implementation that
	  does not rely on the libval library. It is causing a lot of troubles for
	  our downstream maintainers.

	+ /names and $[...] now uses utf8 string operations. By Xavier
          G. (#40, #411, #471, #480).
	+ New setting completion_nicks_match_case (#488).
	+ /channel /server /network now support modify subcommand. By
	  Jari Matilainen (#338, #498).
	+ Irssi::signal_remove now works with coderefs. By Tom Feist (shabble, #512).
	+ /script reset got an -autorun switch (#540, #538).
	+ cap_toggle can now be called from Perl, and fields
	  cap_active and cap_supported can be inspected (#542).
	+ Make it possible to disable empty line completion. By Lauri
	  Tirkkonen (lotheac, #574).
	+ New option sasl_disconnect_on_failure to disconnect when
	  SASL log-in failed (#514).
	- IP addresses are no longer stored when resolve_reverse_lookup is
	  used.
	- Removed broken support for curses (#521).
	- Removed broken dummy mode (#526).
	- Fix terminal state after suspend (#450, #452).
	- Improve Perl library path detection (#479, #132).
	- Reconnect now works on unix connections (#493).
	- Fix completion warnings (#125, #496, FS#124).
	- Fix a crash in the --more-- item (#501).
	- Fix a display issue in /unignore (#517, bdo#577202).
	- Fix a crash in some netsplits (#529, #500).
	- Fix crashes with some invalid config (#550, #551, #563, #564, #587, #581, #570).
	- Add support for SASL Fragmentation. By Kenny Root (kruton, #506).
	- Improve netsplit dumping (#420, #465).
	- Improve responsibility under DCC I/O strain (#578, #159).
	- Fix query nick change on open (#580, #586).
	- Correct a few help texts.

v0.8.21 2017-01-03  The Irssi team <<EMAIL>>
	- Correct a NULL pointer dereference in the nickcmp function found by
	  Joseph Bisch (GL#1)
	- Correct an out of bounds read in certain incomplete control codes
	  found by Joseph Bisch (GL#2)
	- Correct an out of bounds read in certain incomplete character
	  sequences found by Hanno Böck and independently by J. Bisch (GL#3)
	- Correct an error when receiving invalid nick message (GL#4, #466)

v0.8.20 2016-09-16  The Irssi team <<EMAIL>>
	- Correct the name of an emitted sasl signal (#484)
	- Correct the prototype for the 'message private' signal (#515)
	- Corrections in away and hilight help text (#477, #518)
	- /squery and /servlist commands have been restored (#461).
	- Where Irssi would previously only report "System error" on connect,
	  it will now try harder to retrieve the system error message (#467).
	- Fixed issue with +channels not working properly (#533)
	- Fixed crash in optchan when item has no server (#485, bdo#826525)
	- Fixed random remote crash in the nicklist handling (#529)
	- Fixed remote crash due to incorrect bounds checking on
	  formats, reported by Gabriel Campana and Adrien Guinet from
	  Quarkslab.

v0.8.19 2016-03-23  The Irssi team <<EMAIL>>
	! If your cursor keys stopped working, try this first: `/bind
          meta-O key meta2'
	- Fixed regression when joining and parting channels on IRCnet (#435)
	- Fixed SASL EXTERNAL. By Mantas Mikulėnas (grawity, #432)
	- Fixed regression when not using SASL (#438)
	- Fixed incorrect SSL disconnects when using SSL from modules/scripts.
	  By Will Storey (horgh, #439)
	- Fixed regression where proxy_string could not be configured or
	  certain file transfers could not be accepted (#445, #446)
	- Fixed storing layout of !channels (#183, #405)
	- Fixed restoration of bracketed paste mode on quit (#449, #457)
	- Make the usage of meta-O for cursor keys configurable with
	      /set term_appkey_mode off
	  (#430, #459)

v0.8.18 2016-02-13  The Irssi team <<EMAIL>>
	* Modules will now require to define a

	      void MODULENAME ## _abicheck(int *version)

	  method to ensure that they are compiled against the correct Irssi
	  version.
	* The signature of "message private" has been changed to

	      5: server, message, nick, address, target

	  in order to support "self messages". Module authors should
	  implement this change if they are using this signal.
	* Removing networks will now remove all attached servers and channels
          (#45).
	* The proxy module now has an /irssiproxy command.
	* sb_search has been moved to scripts.irssi.org
	* WIN32 has been completely removed (it had not been working and is
          lacking a maintainer.)
	* Garbage Collection support has been removed. This will hardly have any
	  effect for anyone given that it has been unsupported for several years.
	+ CAP SASL PLAIN login is now supported natively.
	+ Paste bracket markers can be requested from terminal with

	      /set paste_use_bracketed_mode on

	+ "Self messages" generated by some bouncers can now be received in the
	  proper window.
	+ Try to split long lines on spaces to avoid words being splitted. Adds
	  a new option: `split_line_on_space' which defaults to on.
	+ Add setting `hilight_nick_matches_everywhere' (#56).
	+ The config parser is more robust and prints out better diagnostics on
	  incorrect config files.
	+ Ctrl+^ (FS#721) and Ctrl+J can now be bound.
	+ Command history can be cleared with /window history -clear
	+ /hilight -mask -line is now supported (FS#275).
	+ CHANTYPES are now supported.
	+ Improved reload speed of ignores.
	+ Add -date feature to /lastlog
	+ irssiproxy can be more easily enabled and disabled.
	+ Expando for hostname (FS#829).
	+ UNIX sockets can now also be specified in the config file.
	+ Disable SSLv3 due to the POODLE vulnerability.
	+ SSL ciphers can now be specified per server.
	+ Added SNI support for SSL.
	- /ignore now respects -pattern on merge (#78).
	- irssiproxy (BNC) module now uses correct line endings.
	- Fix missing lines on large pastes (FS#905).
	- Correctly preserve STATUSMSG prefixes (#291).
	- Fix infinite recursion in key bindings (FS#817).
	- Fix incomplete awaylog caused by buffering.
	- Fix calculation of UTF-8 string length display in some cases.
	- Fix some Perl warnings related to @ISA.
	- EXEC windowitems now get proper references on the Perl side.
	- Incremental help file improvements.
	- ANSI attributes are now properly reset.
	- Fixed regression where text would blink when terminal lacks color
          support.
	- Permit the usage of Freenode extban syntax in /ban (#150)
	- Fixed regression in scriptassist on unload of scripts.
	- Fixed regression in -actcolor %n

v0.8.17 2014-10-11  The Irssi team <<EMAIL>>
	+ Document that SSL connections aren't properly handled during /UPGRADE. See Github PR #39.
	+ Synchronize scripts with scripts.irssi.org.
	+ Performance enhancement of the nicklist as well as the window_item_find function. See Github PR #24.
	+ Disallow unloading of static modules.
	+ Allow UTF-8 characters in /bind. See Github PR #18.
	+ Split overlong outgoing messages instead of silently truncating them.
	  Adds two new options: 'split_line_end' and 'split_line_start'.
	  'split_line_end' contains a string added to the end of line fragments.
	  'split_line_start' contains a string added to the beginning of line
	  fragments. See Github PR #29.
	+ Added special /ignore NO_ACT level to ignore only activity (see /help ignore).
	+ Support for 256 and true color terminals (see Github PR #48).
	+ Support for italics (see Github PR #58).
	+ Rewrote many help files.
	- Fixed various compiler warnings and use of deprecated functions.
	- Fixed Perl API usage and added PERL_NO_GET_CONTEXT to reduce code size.
	- Fixed format_get_text Perl API. See Github PR #23.
	- Fixed gui_printtext_after and term_refresh_*() visibility. See Github PR #22.
	- Fixed issue where UTF-8 characters was corrupted once for every 32k text. See Github PR #12.
	- Fixed redrawing issue with right-aligned statusbar.
	- Fixed use-after-free bug with cached settings values. See Github PR #147.

v0.8.16 2014-05-28  The Irssi team <<EMAIL>>
	+ Add -noautosendcmd to /SERVER and /CONNECT. Passing this option will
	  force Irssi to not execute the content of the autosendcmd chatnet-setting
	  upon connect.
	+ Accept names replies with nick!user@host instead of just nick, if they
	  are enabled (see bug #805).
	+ Set window binds for channel items as sticky when re-creating window
	  binds as part of /layout save. This fixes the bug where previously saved
	  channel windows forgets their window number upon reconnect.
	+ Add experimental support for DNSSEC DANE validation of certificates.
	+ Strip the argument for boolean options (see bug #769).
	+ Freenode have been readded to the list of networks in the default
	  configuration file.
	+ Disabled support for the insecure SSLv2 protocol.
	+ Various documentation enhancements.
	+ Add -ssl_pass to /connect and /server (see bug #305).
	- Fix crashing bug that can happen if the terminal height decreases before
	  the first window is created.
	- Fixed minor compiler warnings.
	- Fixed possible crashing bug when processing an octal escape sequence.
	- Fixed the /ignore -network option (see bug #748).
	- Fixed signal handling for /exec'd commands. Irssi now sends the signal to
	  the process group id instead of the process id.
	- Fixed segfault generated by SSL disconnections (see bug #752).
	- Fix compilation when build with -Werror=format-security. Patch by
	  Jaroslav Skarvada.

v0.8.15 2010-04-03  The Irssi team <<EMAIL>>
	+ Add active_window_ignore_refnum option
	  With active_window_ignore_refnum ON, the current behavior for the
	  active_window key (meta-a by default) is preserved: it switches to the
	  window with the highest activity level that was last activated.
	  With active_window_ignore_refnum OFF, the old behavior is used:
	  it switches to the window with the highest activity level with the
	  lowest refnum. (by Matt Sparks, bug #667)
	+ Show new Charybdis +q list in channel windows (numerics 728 and 729).
	+ Allow servers to belong to multiple networks.
	+ Improve paste detection. Irssi now detects a paste if it reads at
	  least three bytes in a single read; subsequent reads are associated to
	  the same paste if they happen before 'paste_detect_time' time since
	  the last read. If no read occurs after 'paste_detect_time' time the
	  paste buffer is flushed; if there is at least one complete line its
	  content is sent as a paste, otherwise it is processed normally.
	+ Show "target changing too fast" messages in the channel/query window.
	+ Use default trusted CAs if nothing is specified. This allows useful
	  use of -ssl_verify without -ssl_cafile/-ssl_capath, using OpenSSL's
	  default trusted CAs.
	+ Show why an SSL certificate failed validation.
	+ Make own nick and actions use default colour instead of white
	  (by Tim Retout).
	- Change some characters illegal in Windows filenames to underscores in
	  logs
	- Fix disconnects when sending large amounts of data over SSL
	- Show all nicks instead of just the first in an /accept * listing
	  (Bug #704)
	- Make several signals without parameters available to perl again.
	  In particular, this includes the "beep" signal
	  (by Matt Sparks, bug #674)
	- Close the config file fd after saving (by Sven Wegener)
	- Check if an SSL certificate matches the hostname of the server we are
	  connecting to.
	- Fix bash'isms, use command -v instead of which and use bc -l in /CALC.
	- Fix a crash with handling the DCC queue.
	- Fix crash when checking for fuzzy nick match when not on the channel.
	  Reported by Aurelien Delaitre (SATE 2009).

v0.8.14 2009-07-28  The Irssi team <<EMAIL>>
	+ Make /reset an alias for /set -default.
	+ Make /unset an alias for /set -clear.
	+ Allow ctrl+home / ctrl+end to go to the beginning / end of scrollback.
	+ Accept WHOX reply (354 numeric) as a /who reply.
	+ Show numerics directed at channels in the channel window.
	+ The time duration parser is more strict now.
	- Fix out of bounds access in event_wallops().
	- Build fix for OS X.
	- Fix the autolog_ignore_targets logic to work correctly with manually
	  opened log files (see bug #673).

v0.8.13 2009-04-01  The Irssi team <<EMAIL>>
	+ Reject some obviously invalid values in /set.
	+ Add perl bindings for Window::get_history_lines
	+ Use an io channel to write the config file.
	+ Use memory slices instead of memory chunks for text buffer.
	+ Remove methods to create/destroy TextBuffer and TextBufferView and low level
	  api to add/remove lines, scripts should be fine using Window::print_after and
	  TextBufferView::remove_line.
	+ Add print_after method to Window perl object analogous to
	  gui_printtext_after but which also expands formats and forces a full line.
	+ Better mapping of signal parameters to Perl. All signals used in scripts
	  now need to be registered with Irssi::signal_register.
	+ Add public header with interfaces to manage statusbar items (bug #535)
	+ Recode: assume utf-8 encoding for an ascii string in which no escape
	  character occurs (bug #392).
	+ Allow /BAN, /UNBAN, /KICKBAN, /KNOCKOUT if channel is not synced.
	  Requesting ban lists from an unsynced channel will ask
	  them from the server, banning a user whose u@h irssi
	  does not know will ban nick!*@* and only bans irssi
	  knows about can be removed.
	+ Allow storing multiple "other" prefixes such as +q and +a (original patch
	  by JasonX)
	+ Add /set autolog_ignore_targets for cherry-picking targets that shouldn't
	  get logged.
	+ Add support for 16 colors. Formats KBGCRMYW and mirc colors are now
	  mapped to colors 8-15. fe-text translates colors 8-15 to bold/blink+0-7
	  if the terminal supports only 8 colors. If your theme uses one of
	  the high color formats and you really want bold you can change
	  %FMT<string> to %fmt%_<string>%_, it will work fine in all irssi versions.
	+ Better 005 PREFIX support (bug #580).
	+ Display 407 numerics other than "duplicate channel".
	+ Fix display of ratbox-style operspy whois.
	+ Recode outgoing irc away messages (bug #412).
	+ Recode outgoing irc quit messages.
	+ Remove scrollback_levelclear_levels setting and add a 'level' option
	  to 'sb levelclear' to specify a comma separated list of levels.
	+ Add perl __WARN__ handler for scripts (bug #427).
	+ Add Irssi::command_parse_options function to parse options for a command.
	+ Revert recode changes introduced in 0.8.12.
	+ Add completion for /WINDOW SERVER.
	+ Support for reading kicks/msgs from TARGMAX/MAXTARGETS 005 tokens.
	+ Enhancements to the redirections code.
	+ Support for RPL_WHOISACTUALLY (338 numeric) for both ratbox and ircu
	  (bug #428).
	+ -idle option of /notify is gone.
	+ /layout save now makes window-channel bindings instantly effective
	  (bug #35).
	+ /ping without arguments does not send anymore a ctcp ping to a channel
	  (bug #542).
	+ Track IRC operator status of nicks a bit better.
	+ new 'actlist_names' option to add active items names in 'act'
	  statusbar item.
	+ new 'word_completion_backward' command to scroll backwards in the
	  completion list.
	+ add 'list' option to /bind to print all the available commands.
	+ show setter/time in +I lists
	+ apply -usermode before -autosendcmd (bug #548).
	+ reduce memory usage of the scrollback buffer and make the display
	  in /sb status more accurate (higher).
	+ fix data getting dropped when a lot is sent at a time (e.g. when
	  attaching to irssi-proxy, bug #528).
	+ introduce the type Irssi::Irc::Client and signals to communicate with
	  proxy clients to allow for scripting parts of the irssi-proxy.
	+ Add sb_search.pl, a script for /SCROLLBACK SEARCH
	- Fix /NOTIFY list when nick is seen joining (bug #642).
	- Include hostmask in 001 event sent by proxy (bug #650).
	- Be more power-friendly: don't run any always-on <1s timers (bug #641).
	- Don't get confused by a failed /JOIN -window (bug #644).
	- Properly initialize embedded Perl (PERL_SYS_INIT3).
	- Replace invalid utf-8 bytes with U+FFFD when drawing a line.
	- Properly unload the original script when using /script load to reload it.
	  (bug #525, patch by Lukas Mai)
	- Clean up script loading in general:
	   * Don't leak local variables to eval'd code.
	   * Set filename/line number to get better error messages from perl.
	   * Use three-arg open and lexical filehandles to avoid surprises.
	   * Include error reason in message for unopenable scripts.
	   * Don't wrap script code in sub handler { } - this avoids spurious warnings and
	     should at least allow __END__ to work properly.
	  (Patch by Lukas Mai)
	- Fix NETSPLIT_SERVER_REC in signals for Perl.
	- Remove buggy /SCROLLBACK redraw and /SET scrollback_save_formats.
	- Always preserve the active mainwindow when resizing.
	- Ignore DNS not found errors when considering reconnect.
	- Do not strip the comma in a mirc color if it is not followed by a digit (bug #250).
	- Fix building perl module with perl-5.10 (bug #630).
	- fix leak with $L expando.
	- fix possible crash with /script reset.
	- ignore exceptions take precedence over ignores in all cases.
	- honour -channels preference for ignore -replies (bug #227).
	- Fix mode display in whois with unreal (379 numeric) (bug #479).
	- Fix regressions that prevented external modules from building/working
	  (bugs #537 #539).
	- Fix /set hilight_level not taking effect immediately (bug #598).
	- Fix bold, blinking and indentation in /LASTLOG and buf.pl.

v0.8.12 2007-10-06  The Irssi team <<EMAIL>>
	+ Some changes to character set recoding.
	+ Rewrite SSL connection/handshake code.
	+ Remove support for glib 1.x.
	+ Do not send our hostname to the server (bug #488).
	+ Add $tag to 'window' item in default configuration.
	+ Pick up host changes on charybdis and ircu servers (396 numeric).
	+ Show various errors such as "cannot send to channel" and "cannot
	  /msg, user is +g" in the channel or query window, if possible,
	  and always include the user or channel name.
	+ Channel forwarding in hyperion and charybdis is now recognized
	  (470 numeric) and the target channel is joined in the window where
	  the original channel would have been joined.
	+ Add support for the ACCEPT command, which is part of the CALLERID
	  server side ignore system in hybrid7 and derived ircds.
	+ Make /WINDOW GOTO start searching at the window after the active
	  one and stop at the one before (bug #332).
	+ Improve completion for /SET.
	+ Use CASEMAPPING dependent comparison to match channel names.
	  Patch by Jon Mayo (bug #436).
	+ Various improvements to the help files.
	+ Add Perl bindings for some gui_entry methods
	+ Make alt/meta+arrow keys work in recent versions of xterm (bug #496)
	- Fix DCC get when file size is 0 (bug #494).
	- Ignore empty lines when pasting.
	- Fix large file support on AIX (bug #404).
	- Remove broken code that prevents unloading of a script in some cases.
	- Fix logging lines with no target to all logs, broken in 0.8.11.
	- Fix casemapping dependent nick and channel matching (bug #436).
	- Update chanop flag before emitting nick mode changed signal (patch
	  by Johan Kiviniemi)
	- Fix recognition of realnames starting with spaces in /WHO.
	- Show "Target left IRC" error messages fully (instead of reporting
	  no such nick "*")
	- Repair channels_rejoin_unavailable. Enabled by default, this retries
	  joins that failed because of netsplits (channel temporarily
	  unavailable (437), duplicate channel). A few servers abuse 437 for
	  juped channels which should not be retried, you should disable
	  channels_rejoin_unavailable if this is a problem.
	- Display 437 and 407 numerics if channels_rejoin_unavailable
	  is not enabled (bug #495).
	- Don't add the same mask to the /KNOCKOUT list multiple times
	  (bug #510).
	- Use MSGLEVEL_NICKS again for printing a nick change in queries,
	  broken in r2389.
	- Fix some /LASTLOG -before/-after issues.
	- Some fixes to the build system.
	- Fix paste sending the first line twice (bug #405)
	- When parsing a numeric option verify that the whole argument, rather
	  than only the first character, is numeric.
	- Some fixes for notices, actions and ctcps to @#channel and +#channel
	  (bug #46)

v0.8.11 2007-04-25  The Irssi team <<EMAIL>>
	+ Add completion for /WINDOW GOTO
	+ New crapbuster-like "scrollback levelclear" command
	+ irssi now aborts a connection when, on an attempt to connect, the server
	  returns code 432 (Erroneous Nickname), bug #425
	+ Allow identifiers in config file to start with a digit, bug #177.
	+ Modify Irssi::UI::Window::command to restore the original active window
	  only if the command executed has not made another one active, bug #403.
	+ Make awaylog_file respect --home, bug #304
	+ Send /QUOTE immediately if server didn't send the 001 event yet
	+ If dcc_own_ip contains IPv4 address, listen only in IPv4
	+ Negative scroll_page_count scrolls screensize-n lines
	  (Patch by Chris Moore)
	+ Sort nicks with custom prefix by the order defined in isupport in /NAMES
	+ New perl command send_raw_first, patch by ComradeP (Bug 413)
	+ Let the module loader also check for fe_common_$protocol files
	+ Don't wait for all /NAMES replies before syncing if we can't combine
	  queries anyways (Patch by jilles)
	+ Renamed irc.efnet.net to irc.efnet.org
	+ /SCROLLBACK CLEAR accepts the same arguments as /CLEAR
	+ Check if binary exists and can be executed before /UPGRADE
	+ Change default value of override_coredump_limit to OFF
	+ UPTIME command by Lauri Nurmi with some modifications (Bug 458)
	+ Remove CR and LF from Perl commands, to make it harder to introduce a
	  security bug
	+ Remove bookmark on a line when line is removed (instead of moving it)
	+ Don't fallback to generic word completer if the command specific
	  completion list is not empty.
	+ Recognize cursor left and cursor right sequences sent by recent xterm
	- Fix some UTF-8 issues, bugs #452 (Patch by Yi-Hsuan Hsin), #459, #460
	- Fixed segfault on quit introduced in 0.8.10
	- Fixed a bug where tab-completion didn't work with utf8/big5 properly
	- Ignore joins without a nick from broken servers
	- Fix whois_hide_safe_channel_id: only look at the beginning of a channel
	  name, not in the middle
	- Don't assume that 7bit ascii strings are encoded in UTF-8, only
	  validate the strings when they contain octest with highest bit set
	  (patch by Mikko Rauhala)
	- Make random really random when resolving
	- Don't get confused by a join command with too many arguments, keys
	  can't have spaces in them (Bug 437)
	- Don't crash on /QUIT with scripts causing and catching signals on UNLOAD
	- Fix %k and %K mappings in curses frontend
	- Fix bold on monochrome terminals in terminfo frontend
	- Fixed colors with TERM=xterm-{88,256}color in terminfo frontend
	- Fix crash with one line high terminal in terminfo frontend
	- Fix building with srcdir != builddir
	- Don't get confused and keep saying "Netsplit over" on every join for
	  user that only rejoined some channels
	- Fix crash in /EXEC (Bug 439)
	- Fix format string in printtext_dest call from Perl, patch by loafier
	- Fix memory leaks in expandos_deinit by Nicolas Collignon (Bug 419)
	- Detect off_t size using AC_CHECK_SIZEOF because it works also when
	  cross-compiling in autoconf-2.50 and higher
	- Fix failed assertion when the config file is unreadable, patch by
	  Daniel Koning (Bug 164)
	- Removed automatic glib downloading and compiling.
	- Fix /FORMAT -delete daychange so it doesn't print an empty line
	- Forbid /WINDOW SHOW when the target window is sticky rather than when
	  there is a window bound to the container of the target window.
	- Improve interaction between autolog and non autolog logs
	- Recognize local oper mode on ircnet (mode +O)
	- Properly initialize reference count for a new setting (Bug #235)

v0.8.10 2005-12-11  The Irssi team <<EMAIL>>

	* Long delayed release, with lots of changes. Most important ones:
	    + Recode support, by decadix and senneth
	    + isupport (005 numeric), by David Leadbeater
	    + Passive DCC support, by Francesco Fracassi
	    - Many memleak fixes, by Toby Peterson

	+ Moved to subversion
	+ /SET paste_join_multiline ON - When paste detection is enabled and
	  you paste lines which look like they're being copy&pasted from irssi
	  itself, it attempts to merge lines said originally in a single line.

	  How this really works is that all indented lines must have the same
	  amount of indentation. Indented lines are merged to last
	  unindented line. If line gets longer than 400 characters, it's split.
	+ /SET whois_hide_safe_channel_id ON - Hides the channel ID prefix
	  of !channels in WHOIS replies
	+ When reconnecting to server, say that it can be aborted with
	  /RMRECONNS
	+ /WHOIS -<server tag> is supported now
	+ /SET whois_hide_safe_channel_id - removes the ugly IDs for !channels
	  in /WHOIS (default)
	+ If we can't connect to server using given own IP, show the IP to
	  user in the error message. Should help figuring out wrong
	  /SET hostname or /SERVER -host settings.
	+ If channel has more nicks than /SET channel_max_who_sync, don't ask
	  /WHO list to avoid getting kicked out of server (Max SendQ exceeded).
	+ /LOAD script.pl loads the perl script
	+ /IGNORE -network ignores only on specified network
	+ /SET use_status_window and /SET use_msgs_window make the effects
	  immediately
	+ Changed the confusing "ircnet" to "network" everywhere
	+ Don't autoget files sent to channels, unless dcc_autoget_masks is set
	+ Added a default "*" target which matches everything on the server,
	  including NULL items associated with it, by jimmy
	+ /UPGRADE now saves server->version
	+ If available, send who set topic and when to irssi-proxy clients
	+ Cleaned up network list: removed smaller networks, added QuakeNet
	+ New default aliases: MANUAL-WINDOWS, EXEMPTLIST and ATAG
	+ Recode support: /RECODE for manipulation of the conversion database.
	  Setting "term_type" has been renamed to "term_charset".
	  /SET recode OFF to disable recode completely.
	  /SET recode_out_default_charset <charset> to specify the default
	  outgoing charset.
	  /SET recode_fallback <charset> to specify a charset that will be
	  used when the normal conversion is failing.
	  /SET recode_transliterate ON to enable character transliteration,
	  so unavailable characters will be transliterated into something
	  readable
	  <charset> can be almost everything listed by 'iconv -l'
	+ Added transpose_words, capitalize_word, downcase_word, upcase_word
	  key bindings
	+ Avoid accidentally /VER in a channel, by requiring parameter
	- Pasted lines weren't added to command history. Some other paste
	  detection fixes
	- Fixed /BIND escape_char
	- Fixes for Chinese multibyte characters handling and cursor movement
	  by Wang WenRui
	- Pasting multibyte chars was buggy, patch by Kuang-che Wu
	- Fixed handling WHOIS printing once and for all. Everything unknown
	  between "beginning of whois" and "end of whois" events is now printed
	  as whois_special. Removed whois_registered and whois_help, they're
	  printed with whois_special as well.
	- Don't replace channel key when receiving channel mode numeric. It
	  could be a fake key there.
	- Don't crash if dcc chated user changes nick
	- Help files are always lowercased. Make sure /HELP COMMAND works too.
	- /EXEC crashed with 64bit systems. Patch by Soren Jacobsen
	- Handle 432 numeric (errorneus nickname) as "nick in use". Fixes
	  problems with ircnet 2.11 ircd when irssi tries to reconnect using
	  UID as nick. Patch by Petr Baudis
	- /SET -default fixes
	- /DCC SEND didn't actually use /SET dcc_upload_path
	- Fixed /WHOIS -yes (Bug 67)
	- Make /JOIN -tag #channel and /JOIN #channel<space> switch to that
	  channel (Bugs 13 and 93)
	- Fixed readded (changed) hilights being in config twice, resulted in
	  duplicate hilights or removed hilights coming back (Bug 39)
	- Fixed messages to @#channel showed *your* nickmode, not the one of
	  the sender (part of Bug 80)
	- Fixed /KNOCK support
	- Fixed own nick changes in irssi-proxy
	- Fixed /HILIGHT -actcolor -mask (Bug 131)
	- Recognise a param of signal_emit/continue in perl script if it's int
	- Fixed bug 120 where proxy doesn't set the server_rec->away_reason
	- Fixed /join -invite -window bug if there is no invite
	- Fixed bug with time settings where hours actually returned 60*hours
	- Fix multiple entries for local IP in /etc/hosts prevents connecting,
	  patch by eridius (Bug 167)
	- Fixed a bug with /me, use the right arguments for
	  "message irc own_action"
	- Update our own nickrec->gone flag on /away <reason> or on /away
	- Fixed output of /hilight (add a space after -levels if any)
	- Add libtool's -module flag to get built properly on all platforms,
	  by Toby Peterson (Bug 212)
	- Don't apply emphasis on _foo_^ if it's a nick (Bug 52)
	- Fix displaying of ctcp userinfo and clientinfo (Bug 222)
	- Remember alternate_nick and max_whois on reconnect (Bug 181)
	- Fix tr_TR locale problem for glib2 (still a bug with glib1.2)
	  by David Pashley
	- Fixed pasting not using the character translation (Bug 151)
	- Fixed a bug where the channel list to join/rejoin on reconnect
	  gets too long, not all channels will be joined. (Bug 108)
	- Print glib errors nicely, by David Pashley
	- Handle ^Z better, by David Pashley
	- Fixed /eval recursion crashing, by David Pashley
	- Fix notify with more nicks than max_whois_in_cmd (Bug 257),
	  based on patch by Krzysztof Kowalik (Borys)
	- Fixed irssiproxy sometimes missing (parts of) lines
	- Fixed remote /WHOWAS
	- Parse negative time setting values, makes it possible again to do
	  /SET server_reconnect_time -1 to disable reconnecting
	- Compile with gcc4
	- Compile with readonly srcdir
	- Fixed crash if receiving broken privmsg without source
	  (which bitlbee can send if you msg yourself)
	- Fixed crash with invalid TERM and termcap
	- When looking up IP addresses, return random IP instead of the first one

v0.8.9 2003-12-11  Timo Sirainen <<EMAIL>>

	* Fixes a remote crash with:
	    a) non-x86 hardware (one requiring memory alignmentation)
	    b) script using "gui print text" signal (with x86 hardware too)

	+ /SET auto_whowas OFF allows now disabling automatic /whowas when
	  /whois doesn't find a nick (by Borys)
	- If pasted line starts with command char, treat it as command always.
	  Paste detection can go on too easily with lagged connections.

v0.8.8 2003-11-23  Timo Sirainen <<EMAIL>>

	- Just a few fixes to converting old settings automatically

v0.8.7 2003-11-23  Timo Sirainen <<EMAIL>>

	* Settings changes - we have now "time", "size" and "level" setting
	  types.
	    - Level settings should work the same as before.
	    - Time settings can have units: days, hours, mins, secs,
	      milliseconds (or msecs). The units can be combined and written
	      in shorter form as well, for example "5d 30m 10ms"
	    - Size settings can have units: gbytes, mbytes, kbytes, bytes.
	      They can be written in shorter form as well, eg. "gb" or "g".

	  Your existing settings should be converted automatically.

	+ Pasting detection. All keys except CR and LF are pasted as-is into
	  prompt in pasting mode.

	  /SET paste_detect_time controls how closely each others characters
	  must occur for it to be considered pasting. Paste mode goes on when
	  first pasted CR/LF is found.

	  The first line may also contain some command characters. They are
	  executed, but their action in entry line is reverted once pasting
	  is detected.

	  What this means in practise is that even if you have TABs (assuming
	  TAB is completion key) in the first pasted line, they get pasted as
	  TABs.

	  This detection isn't perfect, so if it annoys you it can be disabled
	  with /SET paste_detect_time 0
	+ If pasting more lines than /SET paste_verify_line_count, irssi asks
	  if you actually want to do that. This should be useful to prevent
	  accidental copy&paste mistakes. Setting it to 0 disables this
	  entirely.
	+ Support for sending SSL certificate to server and optionally verify
	  server's certificate. See the -ssl_* options for /SERVER and
	  /SERVER ADD. Patch by Joel Eriksson.
	+ DCC SERVER support by Mark Trumbull
	+ Support for DCC sending larger files than 2GB if supported by
	  operating system (ie. 64bit file support). Receiving has always
	  been possible, but the statistics were wrong with >4GB files
	  if 64bit support isn't enabled.
	+ Better displaying of DCC file transfer statistics.
	- Several other minor fixes and enhancements, see ChangeLog

v0.8.6 2002-11-17  Timo Sirainen <<EMAIL>>

	* Tons of changes, here's only the largest that come to my mind now:

	+ SSL <NAME_EMAIL>
	+ DCC send queues by Heikki Orsila
	+ Better support for !channels

v0.8.4 2002-03-13  Timo Sirainen <<EMAIL>>

	* Continuing to fix my stupid mistakes...

	- When a queried nick did a nick change, it might have crashed irssi
	- read ChangeLog for some other minor changes

v0.8.3 2002-03-13  Timo Sirainen <<EMAIL>>

	- Perl scripts handling channel parts/kicks/quits printed some
	  errors.
	- Connecting to IPv6 servers without IPv4 record didn't work
	- When queries were auto-created by you using /MSG and you had
	  /SET autoclose_query non-zero, the query was always destroyed
	  almost immediately.
	- Fix to some stupid ircds not sending us 001 numeric, but
	  beginning from MOTD

v0.8.2 2002-03-11  Timo Sirainen <<EMAIL>>

	* Changed the list of scripts distributed with irssi. Separated
	  example scripts to scripts/examples/ directory.

	* Hated infobar was removed, the same info is now in topicbar in
	  empty windows. I don't think it would disturb anyone in there.
	  If you still preferred always-empty topicbars, use
	  /SBAR topic REMOVE topic_empty

	+ Added info_eol field to theme. If true, timestamp and server tag
	  are added to end of line, not at beginning.
	+ If -4 or -6 option is used with /SERVER, irssi now forces the
	  connection using the given protocol or fails.
	+ /SET max_wildcard_modes (default 6) - if a wildcard to /OP, /DEOP,
	  /VOICE or /DEVOICE matches more nicks than this, -yes option is
	  required. This is trying to prevent accidental massops. Setting
	  it to 0 disables this check.
	+ Supports now correctly servers which use '.' char as channel owner
	  character in /NAMES list. Also supports multiple mode chars, eg.
	  @+nick (if some server actually used it).
	+ Never ignore kick message if you get kicked from channel.
	+ Sending irssi SIGHUP now does a /RELOAD - useful if you
	  accidentally messed up something which doesn't let you do the
	  /RELOAD inside irssi (eg. /SBAR prompt DISABLE).
	+ irssi-proxy: PROXY CTCP ON|OFF - proxy clients can send this
	  command to specify that they want to handle the received CTCP
	  requests. Useful for specifying who gets to handle DCCs.
	+ Added escape_char /BIND command. The next char after that would be
	  added as-is to input line.
	- Writing lines longers than 1024 chars to input buffer crashed
	  irssi (stupid missing sizeof() bug :)
	- Some UTF-8 fixes
	- Better flood protection for lines with >100 chars
	- Control characters aren't printed as-is in topicbar (or statusbars
	  in general) anymore
	- /OPER can be now aborted by giving empty password
	- Netjoin messages were buggy in +channels
	- Part message parameter for /CYCLE was never used.
	- Don't send -autosendcmd after /UPGRADE.
	- /SET autoclose_query - now only last received private message
	  affects when the query is closed, ie. /WHOIS requests or nick
	  changes don't reset the counter.
	- Foreground ANSI colors weren't working
	- Deleting one character replaced cutbuffer with that character.
	  Also ^Y leaked memory.
	- /SCRIPT LOAD looked scripts from prefix/lib dir, not prefix/share
	  where they were actually installed.
	- Highascii chars in replaces block in theme files could have
	  crashed irssi.

v0.8.1 2002-02-17  Timo Sirainen <<EMAIL>>

	* Expected bugfix release :) Worst thing was that I forgot always to
	  debug why /cat /dev/urandom crashed irssi. Well, found two ways
	  that could make it happen.

	- Irssi was linked with -lcurses AND -lncurses
	- Logging could have produced GLib errors with certain conditions
	- A few compiler warnings fixes

v0.8.0 2002-02-17  Timo Sirainen <<EMAIL>>

	* I really should make releases more often. Once in every two weeks
	  used to be nice. Maybe once in a month would be good now. There
	  was many reasons why this release took this long .. first being
	  addicted to books, then life stuff, then it took forever to figure
	  out that irssi was crashing under solaris (and not because of one
	  of the big changes I made while moving to IRC from solaris box).

	  And now.. well, after this release I'll start working more with
	  the yet another irssi rewrite :) The code is getting too ugly
	  again, and some things need rewriting to support some new features
	  more easily. It will take a while to get it all done, so I'll try
	  to keep updating this "stable" irssi as well.

	  There's too many changes since 0.7.98.3 (and .4 which was just
	  minor bugfix), about 6600 lines in ChangeLog. I'm not going to
	  read all that, so I'll just list the biggest changes that I can
	  remember now.

	  This version was supposed to be called 0.7.99, but since there
	  were so many changes, and I originally though of putting 0.8.0
	  out long time ago, and 0.7.100 would look stupid, I guess it's
	  time for 0.8.0 :)

	+ /UPGRADE - upgrade irssi to new version on-the-fly without
	  disconnecting from server, so other people won't even notice you
	  quit from IRC. This ONLY executes the new binary, it does NOT
	  download/compile/whatever irssi.
	+ UTF-8 support with /SET term_type UTF-8, default is "8bit".
	  It's also automatically detected from system locale (if supported).
	+ Fully configurable statusbar. Yes. FULLY. Don't bother asking if
	  something could be done with it, it can, just ask how. Well,
	  there's a few things I didn't have time/energy to finish:
	  window-specific statusbar groups and support for multiple input
	  lines in screen.
	+ Rewritten keyboard handling. No more the CTRL-X and ALT-x handling,
	  now you can create whatever keyboard combinations your terminal
	  can send to irssi.
	+ Rewritten text buffer (scrollback) handling.
	+ Irssi doesn't depend on curses anymore, so it can be installed
	  anywhere a working terminfo/termcap exists. This also allows us to
	  use all the possible colors terminal has (curses limits to 64), so
	  eg. %0 is now always black background, not the default terminal
	  background (%n).

	  Several systems have also terminfo/termcap database that doesn't
	  support colors, so I've added /SET term_force_colors option to
	  force ANSI-style colors. Note that eg. BitchX does this by default.

	  Getting rid of curses allows also one great thing for you people
	  copy&pasting long urls :) If a long word gets split to two lines,
	  doubleclicking the word selects it from both lines.
	+ Rewritten server event redirections. Before it was pretty easy to
	  mess up irssi's expectations of what server sends, and some things
	  might have stayd in the buffer forever. Especially notify lists
	  messed up /WHOIS requests for the notified people. Now all this
	  should be history and it's a lot easier for scripts to use the
	  redirections as well.
	+ New ICB chat protocol plugin - very basic and doesn't support
	  nicklist, but works. :)
	+ --home and --config parameters to specify alternate ~/.irssi
	  directory or config file.
	+ Scripts can be unloaded separately with /SCRIPT UNLOAD. You can get
	  a list of runnning scripts with /SCRIPT.
	+ /SERVER PURGE [<target>] - purge the output buffer (for given
	  target). Useful for example if you accidentally paste 100 lines :)
	  The buffer is automatically purged if you get kicked from channel,
	  or if you /PART the channel and there's more than 10 lines in
	  output buffer.

v0.7.98.3 2001-03-17  Timo Sirainen <<EMAIL>>

	* Highlighting changes: /HILIGHT -color, /SET hilight_color and
	  /SET hilight_act_color don't accept the numeric color value
	  anymore, the colors must be the %code format (see the beginning
	  of docs/formats.txt). The color can also have background and
	  underline/blinking set (%F%Y = blinking yellow)

	* Statusbar rewrite: Statusbar should finally work well when
	  there's not enough space for it in screen. Least important items
	  start shrinking/disappearing before more important ones, activity
	  list should be always fully visible now.

	  /SET statusbar_xxx settings have been removed, everything is
	  configurable from theme now. Even the texts in the statusbar
	  items. See end of default.theme.

	  FULLY configurable statusbar with possibility to create your own
	  items and support for multiple lines will hopefully come soon.

	+ /WINDOW CLOSE [<first> [<last>] - you can close multiple
	  windows at once now.
	+ Emphasis work with actions now
	+ If there's any unknown settings in your configuration file, irssi
	  asks at startup if they should be removed.
	+ All abstracts in theme files now default to same as in
	  default.theme, so you may override only those you want to change
	  leaving the rest commented out.
	- Irssi crashed when specifying vhost to use (not always?)
	- Fixed one nasty usage of already free'd memory. Hopefully solves
	  some weird crashes?
	- Some perl fixes, Irssi::Nick and "massjoin" signal didn't work
	  properly which broke all auto-op scripts.
	- If one server fails because of DNS error, don't stop reconnecting
	  to entire chatnet.
	- Updated default config to have max_query_chans=1 in undernet to
	  avoid those channel syncing problems.
	- /SERVER didn't autojoin channels if it was used when you weren't
	  connected to any other servers
	- /CONNECT -ircnet didn't load ircnet specific settings correctly
	- /SET scroll_page_count - don't crash if /0 is given. Works now
	  properly if /0.xx is given.
	- ^O did reset only colors, not underlines etc.
	- Several fixes with handling blinking text
	- Irssi crashed almost immediately with NetBSD/Alpha, fixed. Still
	  not sure if it was compiler bug or is my code just non-standard C.
	- Reconnecting in IPv6 server shouldn't change to IPv4
	- Irssi proxy didn't work properly with systems where irssi was
	  compiled with --enable-ipv6 but OS didn't support IPv6

v0.7.98.2 2001-03-04  Timo Sirainen <<EMAIL>>

	+ /LASTLOG: added -case option for case-sensitive matching.
	  -force option is now required to print lastlogs longer than
	  1000 lines.
	+ /BANTYPE -> /SET ban_type. /BAN: -type option added to override
	  default ban type.
	+ /NAMES: -ops -halfops -voices -normal options added.
	  /NAMES without parameters now prints nicklist in active channel,
	  /NAMES ** shows all nicks in all channels.
	+ delete_next_word key implemented, patch by Tinuk
	+ /SET beep_when_window_active - works with /SET beep_msg_level,
	  should we beep when the msg is printed to active window. If msg
	  is printed to some other window it always beeps.
	+ /JOIN #channel and /QUERY nick won't anymore automatically move
	  channel/query to active window but send a notice to user how to
	  move it. This was confusing people who did it accidentally.
	+ /SET autostick_split_windows (default ON) - should we do
	  /WINDOW STICK ON to all new split windows and hidden windows that
	  are created inside it. This hopefully makes it easier to use split
	  windows.
	- All IPv6 problems are hopefully fixed. Everything now keeps both
	  v4 and v6 addresses in memory and at connect() time it's decided
	  which one should be used.
	- /IGNORE * level printed GLib error. /RELOADing printed some
	  unignore texts. Autoignores had some problems.
	- Using /LAYOUT SAVE with split windows crashed irssi at startup
	  when it tried to restore them..
	- /WINDOW SHOW command didn't work properly
	- /LAST -clear crashed if window contained only lastlog lines.
	  Beeping after /LAST -clear also could have crashed.
	- HILIGHT level didn't work with logs.
	- /SET prompt - if $T (target) had %c or something in it, it
	  shouldn't be treated as color code. So color codes can now be
	  used in /SET prompt string itself, but in none of the $variables
	  it uses.
	- Generated help files had joined lines in chapter together but
	  didn't add spaces between lines.
	- Statusbar could have gotten to endless loop when trying to give
	  more space to some item when there was no more available space.
	- When /SET autoclose_windows is ON, don't destroy windows if they
	  have some level set (like /join -w + /part in status window)
	- If GLIB was unpacked to irssi directory, make install tried to
	  install it also.
	- Always save theme to ~/.irssi/ no matter where it was read from.
	- If /SET names_max_width was too low, irssi crashed
	- /CONNECT -ircnet "" - even if someone does this don't make the
	  server tag empty :)
	- /QUERYing #channel that was already /JOINed crashed irssi after
	  a while
	- /SET -clear printed GLib error when done to boolean settings

v0.7.98.1 2001-02-22  Timo Sirainen <<EMAIL>>

	- fixed stupid remote crash with nick_match_msg()

v0.7.98 2001-02-22  Timo Sirainen <<EMAIL>>

	* Theme files aren't fully compatible with old ones, there's a few
	  naming changes and some new items. Added lots of comments and help
	  to default.theme, so creating themes should be a bit easier now :)

	* Perl scripts aren't compatible with old ones anymore. Biggest
	  change is that $object->values()->{xxx} calls are now just
	  $object->{xxx}, but there's a lot of other changes as well.
	  docs/perl.txt is now up to date so you may want to read it.

	* Several settings have changed their names:
	    /SET beep_on_msg -> beep_msg_level
	    /SET activity_levels -> activity_msg_level
	    /SET hilight_levels -> activity_hilight_level
	    /SET noact_channels -> activity_hide_targets

	+ /SET prompt, /SET prompt_window - Specifies the text in prompt.
	  'prompt' is used when channel or query is active in window and
	  'prompt_window' is used with empty windows. You can also use
	  % color codes in prompt.
	+ /EXEC - rewrote it, has now all the same features as EPIC and a
	  few more, like "interactive shell in window" support. See
	  /HELP exec for information.
	+ /SAVEWINDOWS renamed to /LAYOUT SAVE. Added /LAYOUT RESET.
	  /LAYOUT SAVE now saves split windows and queries properly.
	  Windows that contain saved channels are never used for anything
	  else (ie. if the saved channel isn't joined, no other channels
	  can be joined to the window)
	+ /WINDOW SERVER: added -sticky and -unsticky options. If window
	  server is sticky, it will never be automatically changed, and it
	  cannot be changed to anything else without -unsticky option.
	  Should be useful for people who want status or msgs windows for
	  each server.
	+ /WINDOW STICK [ON|OFF|<ref#>] - stick window to specified split
	  window. After setting window to sticky non-sticky windows can't
	  replace the active sticky one. Each split window can have it's own
	  sticky window group.
	+ /WINDOW LEFT, /WINDOW RIGHT - Go to previous/next window in the
	  current sticky window group, or if there's no sticky windows go to
	  previous/next non-sticky window. Alt-Left/Right keys default to
	  these commands now.
	+ /WINDOW NUMBER: -sticky option added. Closing windows before a
	  sticky window won't change refnum of the sticky window and windows
	  after it.
	+ /SET windows_auto_renumber - should window refnums be packed when
	  some window is destroyed in the middle
	+ /SET scroll_page_count - how many lines to scroll with pgup/pgdn.
	  either an absolute value, or if the count starts with '/', it's
	  calculated as lines_in_screen/count. The default is /2.
	+ /SET timestamp_format specifies the format to use with timestamps
	  in "man strftime" format.
	+ Emphasis (*word*, _word_) replacing works better now. It doesn't
	  try to change nicks or any non-words. This time people might want
	  to actually use it :)
	+ Nick completion logic changes, should work better now. Changed
	  meaning of /SET completion_keep_publics to be number of publics
	  to keep instead of time to keep them.
	+ /HILIGHT: Added -priority option (deciding which color should stay
	  in activity list), /SET hilight_level to specify the default level
	  for /HILIGHTs. -word option renamed to -full. Added new -word
	  option meaning to highlight only the found word in line. Removed
	  -nonick option but added -line which means pretty much the same.
	  -actcolor specifies what color to show in activity list, default
	  is the same as -color or if there's no -color it defaults to
	  /SET hilight_act_color. Colors can have background color set with
	  fg,bg. Works with activity list too, useful for example blinking.
	+ DCC rewrite. File names with spaces work properly, you can have
	  multiple dcc chats with same people (or more useful, same nick in
	  different ircnets), /DCC CHAT|GET|RESUME with no arguments accepts
	  the last request, notifies if dcc request was sent to channel,
	  warns about connecting to lowports, /SET dcc_autoget_lowports
	  specifies if autogetting should work with lowports, complains of
	  invalid DCC CTCPs instead of ignoring, /SET dcc_autoresume is like
	  dcc_autoget except resumes the files if possible.
	+ /NAMES can print columns with different widths now. You can change
	  the max. width with /SET names_max_width and /SET names_max_columns
	  settings. Default is max. 6 columns.
	+ /LASTLOG: Added options -file <filename> for writing lastlog to
	  file, -window <ref#|name> for specifying which window's lastlog to
	  print (output is always to active window) and -clear option to
	  remove all lastlog lines from window.
	+ /OPER [<nick> [<password>]] - syntax changed. If password isn't
	  given, it's asked.
	+ /FOREACH server|channel|query|window <command>
	+ /UNBAN <ref#> works. /BAN list shows reference numbers.
	+ /PERL <code> - runs perl code (like /PERL Irssi::print "hello")
	+ /CLEAR -all - clear all windows
	+ /KICKBAN and /KNOCKOUT accepts multiple nicks separated with commas
	+ /SET theme - says what theme was changed to, and complains if
	  theme wasn't found. Setting theme's name to "xxx.theme" now works,
	  too many people tried it with the .theme suffix :)
	+ /WHOIS without parameters in query does now same as
	  /WII <queried nick>
	+ IPv6 updates: /CONNECT, /SERVER, /SERVER ADD: added -4 and -6
	  options for specifying if we should connect to IPv4 or IPv6 address
	  of the server. If -host or /SET hostname is set, irssi determines
	  from it if it should use IPv4 or v6. If irssi still isn't sure of
	  it, it fallbacks to /SET resolve_prefer_ipv6
	+ Logs and rawlogs write to files through special "write buffer". By
	  default everything gets written immediately, but you can make it
	  wait until there's specified amount of data in buffer or write once
	  in a hour or so. Useful for people who want to keep hard disk at
	  sleep at nights but still want to log. /SET write_buffer_mins and
	  /SET write_buffer_kb specifies when to flush the buffer.
	  /FLUSHBUFFER flushes it immediately.
	+ LOTS of other smaller things that should make your life easier :)
	- /SET dcc_autorename OFF didn't work before.
	- Irssi compiled with IPv6 support didn't work properly with some
	  operating systems.
	- Theme saving to home dir didn't work correctly if globaldir
	  already had the same theme.
	- If log file locking failed, irssi always assumed it was locked
	  while it could have been because filesystem didn't support locking
	  or some other problem..

v0.7.97 2000-12-03  Timo Sirainen <<EMAIL>>

	* Added templates for themes - this allowed separation of the
	  actual texts and styling/coloring. See default.theme file for
	  more information. You should remove your old ~/.irssi/default.theme
	  or at least merge it with the defaul.theme.

	* If GLIB sources are found unpacked from some subdirectory, always
	  compile it and use it even if GLIB is already installed somewhere
	  else.

	+ /SCROLLBACK REDRAW - redraw contents of current window according
	  to active formats, ie. changing theme updates the scrollback.
	  This requires /SET scrollback_save_formats ON.
	+ /SET log_theme <theme> - you can specify what theme to use for
	  writing texts to log file.
	+ /WAIT [-<server tag>] <milliseconds> - wait for <milliseconds>
	  before sending anything else to server
	+ /EXEC <command> - execute command and print it's output (stdout
	  only) to screen. (by Tinuk)
	+ Don't indent the next line when long word is split, this should
	  help a bit when copying long URLs.
	+ Remember who set the topic and when, display the info when using
	  /TOPIC.
	+ /SET emphasis - convert _underlined_ and *bold* words (and phrases)
	  to use real underlines/bolds. (by tommik)
	+ While waiting for more netsplits/netjoins, if anything else is
	  printed to screen, print the current netsplit/netjoin messages
	  before that text.
	+ Print multiple identical mode changes in one line
	  (mode +o nick by nick1, nick2, nick3)
	+ /WINDOW ITEM GOTO <name> - sets <name> active in current window
	+ /WINDOW ITEM MOVE <window#>|<name> - moves window item to another
	  window.
	+ /SET autocreate_windows - should we create new windows for new
	  window items or just place everything to one window
	+ /JOIN #already_joined_channel, /QUERY already_queried_nick - same
	  as /WINDOW ITEM MOVE <name>
	+ /SET activity_level, /SET hilight_level - specifies which message
	  levels should be treated as msg/hilight activity. (by tommik)
	+ DCC queries are now created automatically only if /SET
	  autocreate_query_level has DCCMSGS level
	+ If other side replies to our DCC CHAT request with DCC CHAT
	  request of their own (maybe we were inside firewall and other
	  side noticed it), connect to it immediately.
	+ Don't allow more than one identical DCC request, if more is
	  received just update the port of the previous request.
	+ Added KILL handling - user/server kills are now printed formatted.
	+ If server KILLs you, connect back (almost) immediately (don't wait
	  for the default 5 minutes)
	+ Nick completion now completes nicks from all channels in active
	  window, except when completing the first word in line only nicks
	  in active channel are completed.
	+ /SET show_nickmode_empty - when nick has no mode, should we
	  display " " or ""
	+ /SET part_message - default message with /PART
	+ Added -current, -window and -level options to /ECHO
	+ Ctrl-T = transpose_characters
	+ Perl scripting supports now printformat(), ie. user can change
	  the text format with /FORMAT just like any other formats.
	+ Proxy plugin now supports multiple servers, blocks CTCPs from
	  clients behind it and if one client sends message to channel,
	  other clients + proxy itself also receives the message.
	- Netsplit/netjoin printing fixes. Sometimes netsplits were hidden
	  completely and some netjoins were forgotten and printed as normal
	  joins instead.
	- Lag checking broke sometimes when nick was changed
	- Don't close non-autolog logs when leaving channel / closing query.
	- Time formats didn't work in directory name of autologs.
	- Sometimes join to channel didn't ever get synced.
	- IPv6 support didn't work correctly with all non-Linux OSes.
	- Lots of minor fixes and changes to make your life easier.

v0.7.96 2000-10-19  Timo Sirainen <<EMAIL>>

	* new configure option: --disable-curses-windows. Use this if curses
	  always redraws the window when scrolling. This is a weird problem,
	  I'd like to know why it happens. :)
	* Log settings are incompatible with previous versions. You'll need
	  to setup them again. Or the targets are actually the only ones
	  that irssi won't read correctly.
	* Lots of moving and cleaning and rewriting stuff from irc to core
	  so adding other IRC-like protocols (but not IRC) would be easier.
	  This was mostly done to make SILC plugin work.
	* Perl was split to Irssi and Irssi::Irc packages. You'll currently
	  need to use both of them with perl scripts ("use Irssi;
	  use Irssi::Irc;). This might not be needed in future :)
	* Changes:
	   - /SET default_nick -> /SET nick
	   - /FORMAT own_me -> /FORMAT own_action
	   - /FORMAT own_dcc_me -> /FORMAT own_dcc_action

	+ Small tutorial to new irssi users - docs/startup-HOWTO.txt
	+ Proxy plugin works again, thanks to fuchs for fixing it :)
	+ You can now connect multiple times to same server and
	  reconnections will work correctly with them.
	+ Support for half-ops (+h)
	+ Actions will now show up in window activity with hilight or
	  message-color, not the text-color as before.
	+ Added tab-completion for /BIND's commands.
	+ Perl support is now built as runtime loadable module by default.
	  You can still build it statically with --enable-perl=static
	  configure option.
	+ /SET completion_nicks_lowercase - when completing nicks always
	  write the nick with lowercase (uppercase letters are ugly ;)
	+ /BIND <key> /command works now directly instead of needing the
	  "command" id in the middle
	+ /connect + /server server/chatnet completion by tommik.
	+ Keyboard should never get stuck again when receiving huge amounts
	  of text from server that irssi doesn't handle fast enough.
	- Hopefully fixed the problem when Irssi starts eating 100% CPU
	- Fixes to make irssi work with other (older and newer) perl
	  versions than 5.005
	- /MSG -servertag crashed irssi.
	- /BAN could crash when showing bans
	- log_day_change was never printed in logs
	- /mode #channel -oooo... would deop the first 3, and then op the
	  rest.
	- When pressing down key, the command line wasn't saved to history.
	- Closing or moving window didn't update the window activity list.
	- Autologging with same named channels in different networks works
	  now correctly.

v0.7.95 2000-08-13  Timo Sirainen <<EMAIL>>

	* Changes:
	    - /LOG: Removed the -rotate option, it was pretty useless since
	      adding the % formats to file name already tells that the log
	      should be rotated.
	    - WJOIN -> /JOIN -window and WQUERY -> /QUERY -window. Added
	      WJOIN and WQUERY aliases for them in default config..
	+ /SAVEWINDOWS saves the current layout of windows. It's
	  automatically reloaded at startup.
	+ Theme fixes: /RELOAD reloads them, /SET theme changes the default
	  theme, you can have window specific themes with /WINDOW THEME.
	+ Irssi uses now real curses windows, irssi should work now better
	  with non-ncurseses.
	+ Autologging supports log rotating now too, just add the wanted %
	  formats to /SET autolog_path
	+ /MSG nick completion now gives the nicks in right time order when
	  using multiple irc networks.
	+ /SET beep_on_msg now works with all message levels, including
	  HILIGHT.
	+ You can change the default line indentation with /SET indent
	+ /channel add -bots: You can now use @ or + before the nick mask to
	  indicate that bot should have either ops or voices/ops.
	+ Perl scripting:
	    - Added namespaces, you don't have to worry about if someone
	      else also happens to be using event_privmsg function..
	    - You can unload scripts with /UNLOAD <name>.
	    - Running scripts that are already loaded, destroys the old
	      script from memory.
	    - Added Irssi::print_window() function to print text to current
	      window.
	- Don't autoclose window after part/unquery if there was still some
	  channels/queries left in window.
	- Autologging fixes: Don't log WHOIS replies by default
	  (autolog_level = all -crap). And with /msg nick1,nick2 don't log
	  to file nick1,nick2.log but nick1.log and nick2.log separately.
	  It also failed if you hadn't created the base path for the logs,
	  now irssi creates the whole directory path.

v0.7.94 2000-07-24  Timo Sirainen <<EMAIL>>

	* Help files! Use /HELP <command> to see them.
	  They're just very first beta versions so don't expect too much.
	  They were written by Markus Vuori <<EMAIL>> with some help
	  from EPIC's help files :)

	* Changes:
	   - /SET completion_disable_auto -> completion_auto
	   - Changed the names of /BIND commands to be epic-compatible.
	     Also added several new commands.
	   - If --prefix is used, add the default perl library directory to
	     same prefix.
	+ Implemented /WINDOW LAST for changing to last current window.
	+ Added DCCMSGS message level. Actions match now either MSGS or
	  PUBLIC level as well as the ACTIONS level always.
	+ SET print_active_channel - if you have multiple channels in same
	  window, should we always print the channel for each message
	  (<nick:#channel>) or only when the channel isn't active.
	+ Don't print nick changes and quit messages from same nick more
	  than once in the same window (if you had joined multiple channels
	  in same window).
	+ /SET settings_autosave - If set ON, settings are automatically
	  saved when quitting and once per hour.
	+ Don't allow recursive aliases, ie. /alias echo echo hello does
	  print hello instead of going to infinite loop.
	+ Implemented /IGNORE -time <seconds>, patch by fuchs.
	+ /PERLFLUSH now cleans up the Perl interpreter properly (closes
	  all the files opened in perl scripts, etc)
	- Awaylog didn't work right if you did /AWAY multiple times.
	- /NOTIFY -idle sometimes printed the unidle-message even if the
	  nick really didn't stop idling. Fixed several other notify list
	  bugs.
	- Tab-msgcompletion didn't work right after you had used
	  /msg -ircnet nick
	- Fixed the long standing text buffer bug which could sometimes
	  crash irssi if you were unlucky.
	- The channel key given in /JOIN should override the one given
	  in setup.
	- /RELOAD caused several bugs

v0.7.93 2000-07-09  Timo Sirainen <<EMAIL>>

	+ Implemented /BIND [<key> [<action> [<data>]]] command.
	  <key> can be CTRL-x, ALT-X or ALT-SHIFT-X
	  The most useful action is "command", give the command in <data>.
	  You can see the rest of the actions with typing /BIND without
	  any parameters. Some actions might have more than one word,
	  to use them type the action in "quotation marks".
	+ When netsplit is over, irssi prints "Netsplit over, joins: (nicks)"
	  and hides all the real JOIN messages.
	+ /^COMMAND hides the output of the command, it's not written to
	  log either. Good for sending passwords for example.
	+ If you're pasting text to channel and some of it starts with /,
	  Irssi will send the "/command" to channel if it doesn't exist
	  (instead of just printing "unknown command").
	+ --enable-perl[=libdir] to configure - you can specify what
	  directory to install the perl libraries.
	+ Implemented runtime loadable modules. /LOAD loads a module,
	  /UNLOAD unloads it.
	+ You can change statusbar colors with /SET statusbar_xxx.
	+ Added -clear option to /SET.
	+ Tab-completion for /FORMAT.
	+ Ctrl-Y undeletes the last line deleted with Ctrl-U.
	- Reconnecting to server had a few bugs
	- /RELOADing configuration produced a few bugs
	- Highlighting had several bugs
	- Automatic command and option completions had some bugs
	- -option tab-completion didn't work

v0.7.92 2000-06-30  Timo Sirainen <<EMAIL>>

	* Some changes:
	    /REHASH -> /RELOAD
	    /SERVER -add, -remove, -list -> /SERVER ADD, REMOVE, LIST
	    /SET window_close_on_part -> /SET autoclose_windows
	    /HILIGHT -nick -> /HILIGHT -mask (see below)
	+ Automatic completion of /commands. Automatic completion of
	  command -options. Complains about unknown -options.
	+ /MSG [-<server tag>] <nick> <msg> - you can specify what server
	  to send the message to.
	+ Rewrote tab-completion to be modular, it's REALLY easy to add
	  completion to whatever you want. It now handles:
	    - Command completion works better, subcommand completion works
	    - Command -option completion
	    - /MSG completion completes from all IRC servers, so if you had
	      message from ircnet and efnet, irssi will complete with
	      /MSG -ircnet nick1 -> /MSG -efnet nick2
	    - #channel completion works - it completes only channels you
	      have joined or have in setup
	    - File name completion with /DCC SEND (and other commands)
	    - /TOGGLE settings completion
	    - Completion for command parameters or subcommands work right
	      even if the command was typed as alias.
	+ /HILIGHT updates:
	     - -nick highlights only the nick, not the whole line. Works
	       only with public messages.
	     - -mask option matches the text for nick mask (it didn't even
	       work before).
	     - Window numbers in activity list are colored with hilight
	       color.
	     - You can give real color names with -color option. All the
	       "normal" colors can be given, if you want bold colors, use
	       b+color name, like bgreen.
	     - /SET hilight_color specifies the default highlight color
	     - /SET hilight_only_nick specifies if we should highlight the
	       nick or the whole line if -nick or -nonick wasn't specified
	       with /HILIGHT.
	+ /LAST -away checks only lines that came after last time you
	  went away.
	+ You can specify command(s) to be sent automatically to server
	  after connected with /IRCNET -autosendcmd. Useful for sending
	  your password to NickServ.
	+ Added /SET reuse_unused_windows setting, default is OFF. Works
	  only when /SET autoclose_windows is ON. This specifies if new
	  windows should be joined to new window, or if existing empty
	  windows should be used.
	+ /SET lag_min_show -1 disables displaying lag entirely.
	- /SCROLLBACK HOME, END and GOTO commands weren't working right.
	- Closing active window that had channels/queries could crash
	- Using \n with /SET expand_escapes ON didn't work right.
	- Logging HILIGHT messages didn't work
	- The "max. count" parameter in /LAST didn't work right.

v0.7.91 2000-06-14  Timo Sirainen <<EMAIL>>

	+ Ctrl-X changes IRC server in stats/msgs/empty windows.
	+ /JOIN -<server tag> #channel joins to channel in specified server.
	+ /WHOIS automatically sends a /WHOWAS query if nick wasn't in IRC.
	+ if some unknown /command had another / in it, like /usr/bin,
	  send it as normal message. good for copypasting paths :)
	+ If you're not allowed to connect to server (K-lined, no I-line),
	  Irssi won't try to reconnect back to the server.
	+ You can disable window activity notifies in specified channels
	  with /SET noact_channels #chan1 #chan2 .. The activity is
	  displayed if window had a message to you.
	+ Tab-completion works now with /commands and /set variables
	+ /SET close_window_on_part - should we close the window too when
	  /PARTing channel
	+ /SET autocreate_own_query ON - creates query window when you
	  send message with /MSG nick.
	+ /SET mail_counter specifies if we should show mail counter in
	  statusbar.
	+ /SET wall_format specifies the text that's sent with /WALL
	+ If you /SET expand_escapes ON and type \n or \t to text line, they
	  will be expanded to newline and tab.
	+ Ctrl-W deletes word in left
	- Flood was detected wrong - /SET flood_timecheck's argument was
	  supposed to be seconds, not milliseconds.
	- Unignoring autoignored nicks didn't work
	- Logging wrote messages to log file twice
	- /WINDOW MOVE <number> could put irssi to infinite loop
	- ANSI blink code crashed irssi.
	- Replying to DCC GET and CHAT requests didn't work
	- /HILIGHT displayed levels twice, /HILIGHT -channels didn't work
	- /SET ignore_signals wasn't read at startup..

v0.7.90 2000-06-04  Timo Sirainen <<EMAIL>>

	* On the way to 0.8.0 .. Major rewriting/rearranging code. There's
	  some changes in behaviour because I'm trying to make Irssi
	  compatible with EPIC as much as possible (except the scripting,
	  perl should be enough?)

	* DOCUMENTATION! See docs/manual.txt

	  This NEWS file contains only the biggest new features, you should
	  browse through the documentation to find the rest. Some of the
	  parameters of commands listed in this file aren't really up to
	  date since I got a bit bored writing them here too.. They are
	  correct in the manual.

	* Irssi isn't anymore IRC specific client, you could easily take the
	  whole IRC part away and use some other chat protocol instead, or
	  use both at the same time. Currently however, only IRC protocol
	  is supported. See docs/design.txt for more information.

	* libPropList isn't needed anymore - I'm using my own configuration
	  library. This is mostly because different proplists worked a bit
	  differently everywhere and several people had problems with it.
	  It's also yet another extra library that you needed to compile
	  Irssi. New configuration library has several advantages:

	  You can add comments to configuration file and they also stay
	  there when it's saved.

	  It's not nearly as vulnerable as proplist. If some error occurs,
	  instead of just not reading anything it will try to continue if
	  possible. Also the error messages are written to irssi's text
	  window instead of stdout.

	  It can be managed more easily than proplist - setting/getting the
	  configuration is a lot more easier.

	* Coding style changes - I'm not using gint, gchar etc. anymore,
	  they're just extra pain when moving code to non-glib projects and
	  syntax hilighting doesn't work by default with most editors ;)

	  Indentation style was also changed to K&R because of some political
	  reasons ;) And I'm already starting to like it.. :) It forces me
	  to split code to different functions more often and the result is
	  that the code gets more readable.

	  And finally I'm also using `const' all over the place.

	* Signal handlers changed - you don't anymore return value 0 if you
	  wish to stop signal. Instead use signal_stop() or
	  signal_stop_by_name().

	+ Flood protection when sending commands to server works now better.
	  It allows sending first 5 messages immediately, but after that
	  only one message is sent every 2.2 seconds.

	  This is the same flood protection that most IRC servers use, so
	  the only affect this protection has is that when sending a lot of
	  commands to server you won't get kicked out from server because of
	  "excessive flood".

	  This can be changed from settings `cmd_max_at_once' and
	  `cmd_queue_speed'. If you want to disable this for some reason, use
	  /SET cmd_queue_speed 0
	+ Split windows in text version, all the normal ircII /WINDOW
	  commands should work: new, kill, grow, shrink, balance, show, hide
	+ /EVAL <commands> - Expand all the special variables from string and
	  run it. Commands can be split with ; character. See
	  docs/special_vars.txt for more info.
	+ Aliases are parsed just like /EVAL - arguments are in $0..$9.
	+ Text formats are also parsed like /EVAL, arguments used to be in
	  $1..$9, now they're in $0..$8 so it messes up existing themes..
	+ /SET [key [value]] - no more the '=' character. Boolean values
	  also need to be changed with ON/OFF/TOGGLE values (not yes/no).
	+ /SAVE [<filename>] - saves the settings to file.
	  /REHASH [<filename>] - re-read the configuration file on the fly
	+ /TOGGLE <key> [ON/OFF] - same as /SET <key> TOGGLE
	+ /ALIAS [-]<alias> [<command>], /UNALIAS <alias>
	  Show, add or remove aliases. /ALIAS -alias = /UNALIAS alias
	+ /NOTIFY [-list] [-away] [-idle [minutes]] <mask> [ircnet [ircnet...]]
	    -away notifies about away-status changes
	    -idle notifies if idle time is first larger than `minutes'
	     (default is hour) and then it drops down.
            -list lists the notify list entries with all their settings
	  /UNNOTIFY <mask>

	  /NOTIFY without any arguments displays if the people in notify
	  list are online or offline.
	+ /HILIGHT [-nick | -regexp | -word] [-color <color>]
	           [-level <level>] [-channels <channels>] <text>
	    -nick: match only for nick
	    -regexp: `text' is a regular expression
	    -word: `text' must match to full words
	    -color: print the reply with `color' - color can be a bold (^B),
	            underline (^_) etc. too
	    -level: match only for `level' messages, default is
	            publics,msgs,notices,actions
	    -channels: match only in `channels'
	  /DEHILIGHT <ref#> | <text>
	+ /LASTLOG [-] [-new] [-regexp | -word] [-<level> [...]]
	           [<pattern>] [<count> [<start>]]
	    -: don't print the "Lastlog:" and "End of Lastlog" messages.
	    -new: show only lines since last /LASTLOG
	    -regexp: `text' is a regular expression
	    -word: `text' must match to full words
	    -level: what levels to check, like -public -msgs (default is all)
	    <pattern>: text to search for, or all if empty
	    <count>: maximum number of lines to show
	    <start>: skip the last `start' lines
	+ /IGNORE [-regexp | -word] [-pattern <pattern>] [-except]
	          [-channels <channel>] <mask> <levels> <^levels>
	    -regexp: `pattern' is a regular expression
	    -word: `pattern' must match to full words
	    -pattern: <pattern> must match to the message's text
	    -except: *DON'T* ignore
	    -channels: ignore only in channels
	    <mask>: either a nick mask or list of channels
	    <levels>: list of levels to ignore
	    <^levels>: list of levels to NOT ignore
	               (/ignore -except nick notices = /ignore nick ^notices)
	  /UNIGNORE <ref#> | <mask>

	  The best match always wins, so you can have:
	    /IGNORE * CTCPS
	    /IGNORE -except *!*@host.org CTCPS
	+ /LOG OPEN [-noopen] [-autoopen] [-targets <targets>] [-window]
	            [-rotate hour|day|month] <filename> [<levels>]
	    -noopen: create the entry to log list, but don't start logging
	    -autoopen: automatically open this log file at startup
	    -targets: log only in specified channels/nicks
	    -window: Log this window
	    -rotate: Reopen the log file every hour, day or month. This
	             makes only sense if you specify date/time formats
		     to file name.
	    <filename>: File name where to log, it is parsed with strftime(),
	                so %d=day, etc. see "man strftime" for more info.
	    <levels>: Defaults to ALL
          /LOG CLOSE <ref#> | <fname> - close log and remove from log list
	  /LOG START <ref#> | <fname> - start logging to file
	  /LOG STOP <ref#> | <fname> - stop logging to file
	  /LOG - display the log list
	  NOTE: Log files are locked after opened, so two irssi's can't
	  accidentally try to write to same log file.
	+ /WINDOW LOG ON|OFF|TOGGLE [<filename>]
	  Start/stop logging window, same as /LOG OPEN -window. If file name
	  isn't given, it defaults to ~/irc.log.<windowname> or
	  ~/irc.log.Window<ref#> if window doesn't have name.
	  /WINDOW LOGFILE <filename>
	  Creates the entry to log list, same as /LOG OPEN -window -noopen.
	  Also, if /WINDOW LOG ON is used it starts logging to this file.
	+ /SET AUTOLOG ON|OFF|TOGGLE
	  /SET AUTOLOG_LEVEL <level>
	  /SET AUTOLOG_PATH <path> - expandos can be used, $0 is the target.
	  Enables automatic logging, files are automatically created as
	  needed and after some time of inactivity, they are closed. If you
	  are using multiple servers, it makes sense to use the server tag
	  as part of the file name, for example ~/irclogs/$tag/$0.log (this
	  is the default).
	+ /SET window_auto_change - if enabled, irssi will automatically
	  change to automatically created windows (like queries). It will
	  also clear your command line and put it to command history so that
	  you don't accidentally write anything to wrong window. You'll get
	  the command back by pressing up arrow.
	+ /SET show_quit_once - show quit message only once instead of in
	  all channel windows the nick was joined.
	+ Server reconnections work better. It will now automatically set
	  your previous user mode and away message (and rejoin the channels,
	  which it already did before) after reconnected. If you use /SERVER
	  to connect to different IRC network, none of this will be done.
	+ /CAT <filename> - prints the file to screen
	+ /SET query_auto_close <secs> - automatically close queries after
	  <secs> seconds. It won't close queries that have unread messages,
	  and it won't close queries in the active window.

v0.7.28 2000-03-11  Timo Sirainen <<EMAIL>>

	+ irssi-text: New improved "text widget". It takes less memory and
	  if you resize the terminal horizonally, the text automatically
	  changes to right size. Several other changes too:

	  /CLEAR only clears the screen, you can still scroll the window up.
	  /SCROLLBACK, or the default alias /SB:
            /SB CLEAR - Clear screen, free all memory used by texts in window.
            /SB HOME - Jump to start of the buffer
            /SB END - Jump to end of the buffer
            /SB GOTO [[-|+]line#|time] - Jump to specified line or timestamp.

	    -100 jumps back 100 lines, +100 jumps forward 100 lines, or
	    100 simply jumps to 100. line in scrollback.

	    Time is the format [dd.mm | -<days ago>] hh:mi[:ss]

	    Examples:
	      /SB GOTO 15:00 - Jump to first text that came after 15:00 today
	      /SB GOTO -1 15:00 - First text after 15:00 yesterday
	      /SB GOTO 1.2 - First text in 1. February
	      /SB GOTO -100 - Jump back 100 lines
	      /SB GOTO +100 - Jump for

	+ After lost connection to server and reconnected or changed the
	  server manually with /SERVER, Irssi will rejoin back to the same
	  channels that you were in before disconnection. They will also be
	  placed to same windows they were, even if you were in same channel
	  in multiple servers.
	+ /SERVERS and disconnect dialog displays also servers that are
	  being currently connected and waiting reconnections. You can remove
	  them with /DISCONNECT <tag>.
	+ If you are in multiple irc servers and the active server of the
	  window isn't the same as where the message came from, the message
	  is prefixed with a [server tag].
	+ If you don't specify the path for Perl scripts, Irssi tries to
	  find them from ~/.irssi/scripts/ or /usr/lib/irssi/scripts/
	  directories. Irssi will also run automatically scripts in
	  ~/.irssi/scripts/autorun/ at startup. Several other updates to
	  Perl support too.
	+ Support for ircII translation tables, /set translation <file>
	  See /usr/share/ircII/translation/* (at least in Debian)
	+ /ACTION <target> <text> - Send action to target (like /ME), target
	  is either #channel, nick or =dcc_char_nick
	+ 5 CTRL-C's in a row quits irssi-text.
	+ %| in themes marks the line indentation position - works only in
	  irssi-text for now..
	+ You can have several msgs/status windows, one for each server.
	+ Option: start GNOME panel applet at startup
	+ --without-gtk option for configure disables building GTK frontend
	+ /LAST -new shows only the texts that came after latest /LAST.
	- Autojoining doesn't switch automatically to the joined channel's
	  window (try #2 :)
	- Several (Perl) compilation problems fixed.
	- Text hilight color was dark grey, changed to white..
	- /LAST doesn't display the texts found from previous /LAST blocks
	- Fixed a few memory leaks

v0.7.27 2000-02-25  Timo Sirainen <<EMAIL>>

	* Perl support - finally! Took only a year or so to imlement it ;)
	  Well, I could have done it ages ago but it wouldn't have had
	  all the flexibility it now has - you should be able to do almost
	  anything with perl scripts. See DOCS/PERL for some documentation
	  and examples/ directory for a couple of example scripts.

	  This is the very first version and I haven't even tested that all
	  functions work correctly! Any suggestions are welcome. I don't
	  really like the values() functions so if someone knows better ways
	  to do them I'd really like to hear.

	  BTW. I haven't had time to learn Perl well yet, so my scripts are
	  probably not the best examples.. :)

	  If for some reason you don't wish to use Perl, you can disble it
	  with giving --disable-perl to configure.

	+ /CYCLE [#channel] - parts/rejoins the channel
	+ Autojoining doesn't switch automatically to the joined channel's
	  window.
	+ Server tag generation is a bit smarter now.
	+ irssi-text: Resizing terminal works now right even if your curses
	  don't have resizeterm() function.
	- /NAMES crashed when done in a non-channel window
	- irssi-text: Resizing terminal when irssi had some empty windows
	  messed them up..
	- toggle_show_nickmode didn't actually do anything :) It was
	  always on..

v0.7.26 2000-02-19  Timo Sirainen <<EMAIL>>

	- Space (and maybe other keys) didn't work when caps/numlock was on.

v0.7.25 2000-02-19  Timo Sirainen <<EMAIL>>

	+ /WQUERY - create query to current window
	+ Irssi doesn't close the window anymore when using /PART
	+ irssi-text also displays user's address in topic bar in queries.
	+ /NAMES list is now displayed sorted
	+ irssi-text: /WINDOW MOVE PREV|NEXT
	- Topic bar sometimes displayed some other channel's topic if the
	  channel didn't have a topic.
	- Irssi automatically changed to auto-created query windows..
	- When using /WINDOW CLOSE it didn't change to different window
	- Made fontset_load() optional - it broke some fonts..
	- Using Ctrl-B (bold) didn't move the cursor

v0.7.24 2000-02-19  Timo Sirainen <<EMAIL>> [stable]

	+ French translation
	+ Updated Brazilian Portuguese translation translation, now with
	  the right pot file name :)
	+ Using fontset_load() instead of font_load(), helps with using
	  some fonts (by <EMAIL>)
	+ /TS - display topics of all channels you're joined
	+ Automatically change to the created window
	+ Option: Show op/voice status in channel messages before nick.
	+ irssi-text: Displays topic bar op the top of the screen -
	  /set toggle_show_topicbar = yes|no
	+ Recognize +a (anonymous) and +r (reop when opless) modes
	+ Don't allow any setup file changes or log writing if another irssi
	  session is running.
	+ /whois without any arguments gives a whois of yourself
	- Irc network list was still corrupted in channel dialog.
	- /LIST dialog - users column is now sorted numerically
	  (10 shows after 9, not after 1)..
	- Log setup dialog: Clear all button was over Client errors
	  toggle button.
	- /LAST's output displayed some crap in log file.
	- irssi-text should work better with other curses libraries than
	  ncurses
	- irssi-text should work better with non-black backgounds
	- Fixed tab completion when completion char was comma
	- Couple of configure bugs fixed
	- Specifying source host (vhosts) didn't work.
	- DCC resume had been broken quite a while

v0.7.23 2000-01-23  Timo Sirainen <<EMAIL>> [stable]

	+ channel's key (+k key) is displayed in irssi-text's statusbar if it
	  has one.
	+ Nick hilight detector is a bit smarter now, for example if your
	  nick happens to be "its", "it's blahblah" doesn't trigger it..
	+ colorless irssi-text (/set colors = no): activity list is split in
	  two, Act and Det lists. Det displays list of windows where there's
	  new messages for you.
	- /LAST without any parameters crashed
	- if queried nick was changed, GUI didn't notice it.
	- config file was invalid in .22
	- irssi text widget didn't work in .22
	- dcc transfers always displayed 0.00kB/s in .22

v0.7.22 2000-01-16  Timo Sirainen <<EMAIL>> [stable]

	+ configure displays a summary of things to compile
	+ /set colors = yes|no, sets colors on/off in irssi-text
	+ /window goto active now finds first the window with the higest
	  activity (msgs to you -> msgs -> rest). Alt-A is also default key
	  shortcut for this
	+ When connection is lost to server, irssi will remember the channels
	  in windows. After reconnected, (auto)joining to same channels will
	  join the channels to the old windows.
	+ Improved hilighting: You can specify what color to hilight the text
	  with, to channel field type the (mirc) color number, like
	  "4 #blah" hilights the text with red in channel #blah, both color
	  and channel(s) are optional. You can also hilight nicks' colors, to
	  text field type "NICK:nick!mask", like NICK:nick, or
	  NICK:*!*@*.blah.fi hilights people from blah.fi domain
	- Modeless channels (+channel) didn't get synced ever..
	- Some kB/s messages displayed wrong values when resuming DCC
	  transfers. Also, kB/s is now displayed with two decimals
	- "Day changed to 00-10-2000" .. month was wrong. No Y2K bugs
	  however ;)
	- List of ircnets was displyed wrong in server dialog.
	- Userhost replies didn't handle ircops right..
	- Doesn't quit when receives SIGHUP - some window managers send it
	  when restarting itself (Afterstep)
	- Specifying "source host IP" didn't work (vhosts).
	- Using ctrl-b etc. didn't move the cursor forward..
	- Don't try to compile GTK parts of plugins if we don't even want
	  build GTK irssi
	- Doesn't crash when trying to create DCC dialog after being
	  disconnected from IRC server
	- Modeless channels (+channel) didn't get synced ever..
	- Some transparency fixes, it's a lot faster now when moving the
	  window, but it's still too slow when creating it, not sure why..

v0.7.21 1999-12-20  Timo Sirainen <<EMAIL>> [unstable]

	+ Irssi-text: Channel activities are displayed with different colors
	  in statusbar
	+ Keeps track of "wanted nick", ie. the nick you specified in the
	  setup or to /server or /nick. When reconnecting to server it always
	  tries the wanted nick before falling back to alternate nicks.
	+ IRC Network specific settings: nick, username, realname,
	  max. kicks/modes/msgs per command.
	+ Transparency works
	+ Automatic logging when you're away. Set it on/off from settings/misc
	+ /connect and /server changes the server in the current window if
	  the window isn't channel or query.
	+ Wallop actions are displayed right
	+ Ctrl-N/P keys change to previous/next window
	+ Polish translation updated
	+ /channel next, /channel prev - changes to next/previous channel in
	  the current window. Ctrl-X is by default bound to /channel next.
	- /WHO could crash irssi
	- /join !!channel crashed

v0.7.20.1 1999-11-28  Timo Sirainen <<EMAIL>> [unstable]

	* I just started #irssi in irc.openprojects.net too.. It's still in
	  IRCNet too, don't know yet if I'll keep both or drop the other one..

	+ You can use %n as current nick with aliases
	- Closing a window with split windows open crashed
	- Channel widgets weren't being updated when joined to channel in
	  empty window
	- configure didn't check if we wanted to build MySQL plugin or not,
	  now it's built only if you give --with-mysql to configure
	- Using the whois, ping, etc. buttons in queries crashed

v0.7.20 1999-11-27  Timo Sirainen <<EMAIL>> [unstable]

	+ Polish and finnish translations started

	+ SQL plugin which doesn't do much, currently supports MySQL only.
	  Meant to be used by other plugins.

	+ Botnet to bot plugin - it should already be possible to create a
	  big bot network with this (each bot having multiple clients +
	  uplink). The functionality is limited only to BCAST message for
	  now which sends a message to all bots. Read docs/botnet.txt for my
	  plans for it :)

	- If plugins failed in initialization (plugin_init()), irssi could
	  crash.
	- Server settings (nick, realname, etc.) were saved to different
	  place in configuration file than where they were read from..
	  So, saving them didn't really work.
	- Plugin autoloading didn't work
	- When trying to show channel's window from panel and you weren't
	  using helvetica font (itext's internal default), irssi crashed..
	- Irssi crashed if you didn't have menubar enabled and didn't
	  compile with gnome.
	- When invalid theme was found from global directory, irssi
	  complained about it every time. Now the fixed theme is saved to
	  ~/.irssi/ directory and used thereafter.
	- Deleting ircnets didn't work right

v0.7.19 1999-11-20  Timo Sirainen <<EMAIL>> [unstable]

	* Text formats changed - they should be compatible with epic/bx now.
          See docs/FORMATS for more information

	+ Internationalization support - finally. No languages yet though..

	+ /window new split creates a new splitted window
	+ Automatic text replaces, useful for things like :9 -> :) .. This
	  is actually almost same as completions, except they are activated
	  with different keys..
	+ Nicklist popup menu is configurable
	+ ~/.irssi/startup - add all commands here you want to run at startup
	+ Much more levels for ignoring/logging/etc. See docs/COMMANDS for a
	  list
	+ Ignoring joins, parts, etc. work, ignoring channels work
	+ Automatically loading plugins at startup works in irssi-text and
	  irssibot too
	+ Autoaccept dcc get/chat from given nick/address
	+ /help
	+ Server/Links dialog, displays list of servers in tree view.
	  Doubleclicking a hub asks the servers behind the hub (doesn't seem
	  to work in efnet)
	+ /server +irc.server.net does the same as /connect irc.server.net
	+ Nicklist is resizeable
	+ Buttons for closing and moving window left/right
	+ Query windows display nick's address in topic widget and the
	  address isn't displayed in every msg in query windows.
	+ It's possible to add bold/colors/etc to default quit message
	+ MIRC colors are finally displayed with right colors
	+ You can run multiple command in alias, separate them with &&.
	  You can create a & character with \&
	+ Hilighting changes: Your own /msgs won't trigger channel activity,
	  received private messages get the "new text" color
	+ /MODE accepts multiple modes at once and they're split automatically
	  to 3 modes/command, like /MODE #chan +oooooo nick1,nick2,.. is
	  split to two commands which are sent to server.
	+ /KICK, /MSG, /NOTICE, /CTCP and /NCTCP are also automatically split
	  into multiple real commands. /KICK can kick max. 4 nicks per
	  command, privmsg/notice can send max. 3 nicks per command.
	+ Option: Show timestamps in msgs.
	- Joining to channel from channels dialog that had password set
	  didn't work.
	- When scrolling, Irssi text widget sometimes left black spots in
	  text area if some other window was overlaping it.
	- DCC resumes still didn't work
	- Fixed some memory leaks

v0.7.18.1 1999-11-04  Timo Sirainen <<EMAIL>> [stable]

	- Window didn't scroll if you were using GtkText
	- Resuming DCC transfers could mess up the existing transfers with
	  the same nick..
	- Some PONGs were displayed on screen if you were enough lagged..
	- --help works now without gnome (gtk/text versions)
	- Sending data to irc server/dcc chats/proxy's clients/etc. won't
	  block forever anymore - I once got it to happen with proxy..
	- Some fixes for channel limit/key widgets above nicklist
	- Speech plugin works now right with timestamps enabled
	- irssibot (gui-none) doesn't crash at startup anymore and it doesn't
	  link with ui-common anymore. Also added a --load / -l command line
	  option to specify what plugin to load at startup. Default = bot

v0.7.18 1999-10-18  Timo Sirainen <<EMAIL>> [stable]

	* Finally a version I dare to call stable :) Just a bugfix release
	  for 0.7.17 but it had only a few problems..

	* Status window is now off by default

	- Sound and speech plugins weren't working.
	- Proxy plugin shouldn't crash now while not connected to server
	- Using some menuitems crashed when using the popup menus instead of
	  menubar.
	- Removed a Gdk-Critical warning when opening themes dialog without
	  GNOME
	- When resuming DCC transfers the average transfer rate was incorrect
	- If you tried to connect to server while themes dialog was open,
	  it crashed.
	- Several problems fixed with changing background pixmaps
	- Fixes for building from different directory
	- Trying to save theme crashed..

v0.7.17 1999-10-16  Timo Sirainen <<EMAIL>> [unstable]

	+ Irssi text widget! (replaces zvt)
	   - supports proportional fonts
           - FAST
           - background pixmaps (scaled, tiled, scrollable, shaded)

	+ Proxy plugin - Unlike other irc proxies, this is more than just a
	  simple proxy. You can connect to it from multiple clients at the
	  same time, but each client will use the _same_ irc session, so you
	  can keep one irc client open all the time in your home, one at
	  work, one at wc, etc. All the clients get the same "normal"
	  messages from server, but if you request a /whois or /who or some
	  other commands, the reply will be sent to only the client that
	  requested it. Check README how to use it.

	+ Irssi is now much faster catching up things after joined to
	  channel, it asks channels' MODE and WHO first, all with the same
	  command (WHO #a,#b,#c), after them it asks the ban lists etc. less
	  important things.
	+ Workaround for GTK themes eating X server's memory
	+ Command line arguments:
		-c server [-p port] : connects to server at startup
		-! : don't autoconnect to any servers
		-n : specify nick to use (override setup)
	+ Server lag displayed in statusbar, you can also set irssi to
	  automatically disconnect from server if it is too lagged
	+ Channel limit and key is displayed above nicklist
	+ Number of ops and total number of nicks in channel is displayed
	+ Nicklist background color can be changed
	+ Each window can have it's own command history buffer
	+ Try to let the server disconnect the socket (5 sec timeout) to make
	  sure that quit message gets through.
	+ Improved /gwhois dialog
	+ Setup dialogs are resizeable
	+ You can specify what port to use with DCC.
	- Channel dialog fixes: after editing channel, it was moved to the
	  end of the list, opening multiple channels edit dialogs didn't
	  work right
        - Reconnecting to server didn't work (always)
	- Giving multiple nicks for /gwhois messed up irssi

v0.7.16 1999-09-13  Timo Sirainen <<EMAIL>> [unstable]

	+ Started bot plugin, it has simple user management functions and
	  auto-opping/voicing done (but it does it well :)
	+ "channel synced" text doesn't trigger channel activity anymore
	+ Rawlog displays where event was redirected
	+ /wjoin - you can join multiple channels in same window
	+ /window goto #channel - moves you to window with the
	  channel, query or dcc chat
	+ /window goto active - moves you to first window with activity
	+ /list and /names complains if they're run without any arguments,
	  -YES overrides this
	+ Giving -nogui parameter to /list and /who commands doesn't use the
	  GUI dialog
	+ All the dialogs that have clist widget: you can resize columns and
	  sort the list by clicking the headers
	+ /list and /who dialogs displays total number of items and the list
	  is searchable
	+ Autojoining to channels work with irssi-text too
	+ /gwhois dialog has now refresh button, /gwhois is used when clicking
          whois from nicklist popup menu
	- Restoring saved window size didn't work very accurately, restoring
	  position also had some problems..
	- Rawlog doesn't crash anymore if not connected to server
	- Notifylist and checking of who uses your nick uses WHOIS again,
	  WHO didn't display user info unless s/he was -i or in same
	  channels..
	- You had to run /list a couple of times until it worked..
	- WHO was sent to people who joined channel to find out who they
	  were, unfortunately it had a small bug and didn't work..
	- DCC didn't work with IPv6

v0.7.15-3 1999-08-31  Timo Sirainen <<EMAIL>> [unstable]

	* _TOO_ many bugs in .15, mostly compilation problems, I really
	  should test things better when I release them. This will be the
	  last time, I swear :)

	+ Rawlog window, /rawlog <file name> also saves it.
	+ --without-imlib configure switch
	- After opened themes dialog, "(none)" window appeared, after opening
	  it, irssi crashed.
	- "day changed" message was displayed at startup
	- Addresses in DCC connections were displayed wrong
	- Didn't compile without gnome
	- The first .15 didn't compile without IPv6 support, -2 fixed it

v0.7.15 1999-08-29  Timo Sirainen <<EMAIL>> [unstable]

	* Only week since last version, much better :) Lots of internal
	  changes, hope they work right. No "weird crashes" found since last
	  version, 0.8.0 can't be too far away :)

	  CVS is also working again, no anonymous but I can give access if
	  someone wants.

	+ IPv6 support - yet another thing irssi is one of the first to
	  support ;) Give --enable-ipv6 switch to configure to compile it.
	  Because of IPv6 addresses naming style (xxx:xxx:xxx..), /server
	  server:port doesn't work anymore, you have to use /server
	  server port instead.

	+ Sound plugin updates, should work much better
	+ Default config file is build into irssi, it's used if no other
	  configuration file is found
	+ Implemented "idle queue", list of commands which should be sent to
	  server when there's nothing else to send.. Changed CTCPs to use
	  this.
	+ "massjoin" signal is sent when people join to channel, if many
	  people join to channel quickly (netjoins), it waits for a while
	  before everyone are joined and then sends the signal. This is used
	  to update nicklist more quickly and some other internal stuff.
	+ /msg <tab> completion: after you send msg to someone, the nick will
	  go first in completion list.
	+ Giving --with-servertest to configure now builds test irc server
	  which you can use to try crash irc clients :)
	+ /sv displays system name and revision and irssi's website url
	+ You can give server password to /server as /server server port pass
	+ /unban completion, eg. /unban *!*@*.de unbans *!*@hu232hu2.blah.de
	+ /rmreconns removes all servers from reconnect list. I hate it when
	  some server is down and irssi tries to reconnect it every 5 minutes
	  and there was no way to cancel it..
	+ Displays day change message in all windows if you're using
	  timestamps
	+ Realname is displayed in statusbar when mouse is moved over nick
	  in nicklist
	+ /last displays the last buffer only from the current window
	+ option: beep when you receive private message
	+ Typing /dcc without any arguments is same as /dcc list
	+ Some code rearranging, moved more code to ui-common from gui-xxx
	+ IPv6 for /ban, it bans all the last 64k addresses .. not sure if
	  it's THAT good idea but did it anyway.
	- DCC resume was broken.
	- If someone quit from one ircnet but stayed on another, the nick was
	  removed from both ircnets' channels.
	- Irssi tried to find it's default config from
	  $prefix/etc/irssi/irssi/config (one irssi too much :)
	- You couldn't use ~/ when saving window buffer
	- Trying to save window buffer twice crashed irssi
	- ZVT transparency couldn't be removed on the fly
	- Using find/new/close buttons in toolbar crashed
	- Doesn't complain anymore about "You're not channel operator" with
	  some irc networks that don't understand e or I modes
	- /ban removed ident-character from username (~blah@ -> blah@) so bans
	  didn't work..
	- /knockout calculated the time left wrongly.
	- irssi added -I/usr/include to compile parameters which broke
	  compiling with several platforms..
	- Irssibot notified about new development version when there was none
	- Some problems/crashes fixed with plugin support
	- --without-socks didn't do anything..
	- Password should first be sent to server first, not after nick/user.
	  At least MUH (irc proxy) didn't like it.

v0.7.14 1999-08-22  Timo Sirainen <<EMAIL>> [unstable]

      !!! My E-mail <NAME_EMAIL>, don't use the old one
      !!! anymore!

	* Hm.. Again a month since last version, 3 weeks should be max :)
          Hopefully this one will be bugfree, so I could finally release a
	  "stable" version.. (somehow I think I'll end up with 0.7.14-2
	  anyway.. ;)

	* Irssi uses now libPropList to read and save configuration file, so
	  you need to have libPropList installed, it's also in different
	  format so your old config file doesn't work anymore.

	  Configuration file is located now in ~/.irssi/config file.
	  Themes are also now stored in separate files in ~/.irssi/*.theme

	+ Irssi can now notify you about new versions, you can also directly
	  download them with DCC. (This will probably be changed to HTTP
	  instead of using irssibot in IRC..)

	+ User interface changes (settings, menus) as suggested by
	  James M. Cape <<EMAIL>>
	+ You can use ctrl-b,c,g,v,- when setting realname
	+ /version [server] - prints irssi version and irc server's version
	+ /ver [nick/channel] - sends ctcp version to nick/channel
	+ /sv [nick/channel] - sends irssi version text to nick/channel
	+ Added widget depends to several places, changed several modal
	  dialogs to use gui_widget_depends() instead
	+ Added Socks5 initialization, maybe it works now?
	+ You can specify what host address you want to use if you have many..
	+ Away and kick reason dialog have history
	+ irssi-text option: activity list can be sorted by window number
	  You can change this with /set toggle_actlist_moves=yes/no
	+ /msg <text><tab> completes now people in current channel too
	+ You can set channel password in channel dialog
	+ /SET [key [=value / [key [key..]] - /SET displays all settings,
	  /set key key2 displays values of key and key2, /set key=value sets
	  key to value.
	+ DCC GET now gets all the files coming from user if file name isn't
	  specified. DCC CLOSE also can close all dcc connections from user.
	+ The whole usermode is now displayed in statusbar, it used to display
	  only the modes it knew (+iwsr)
	+ Ctrl-N and Ctrl-P go to next/previous window
	- When using zvt and joining to new channels, window size grew bigger
	- /msg <tab> completion was a bit buggy, if someone sent you multiple
	  messages, you had to press tab multiple times until the nick changed
	  to someone else..
	- Default format for signon time in whois displayed nick instead of
	  the signon time..
	- Disconnecting server while it was still trying to connect hung irssi
	- If old configuration file wasn't found, irssi (could have?) crashed
	  on startup .. Could this really happen?!? Why did nobody tell me??
	- irssi-text finally handles screen resizing right
	- Gnome panel applet works with "old" (like non-cvs now :) panels
	  too.
	- If you left from channel before syncing was done, syncing stopped
	  working after it..
	- Removing ban exceptions didn't update irssi's internal list
	- Rejecting dcc chat didn't work properly, when receiving reject
	  get/send irssi didn't remove it from dcc lists
	- Save/find dialogs weren't working after being closed.
	- irssi-text complained about "Not enough parameters given" when
	  pressing entry in empty line
	- Mirc colors weren't removed properly for logs and could have
	  crashed irssi
	- Using /ban with mask (x!x@x) instead of nick crashed
	- gui_widget_depends() wasn't working properly - didn't harm much :)

v0.7.13-2 1999-07-22  Timo Sirainen <<EMAIL>> [unstable]

	* Again, a small bugfix release

	+ You can specify what string to send to IRC proxy after connected,
	  this lets at least some proxies work with irssi.
	+ Notifylist now displays which irc network nick joined/left (or if
	  unknown, just IRC)
	- After closing some window, the numbers in window tabs didn't get
	  updated
	- /window next and prev didn't work properly
	- status/msgs windows got destroyed a while after joining to channel..
	  or simply by doing "/mode (status)" command ..
	- We don't try to DCC SEND file via dcc chat if the other side is
	  using mirc ctcps.
	- Default setting or autodetection of mirc ctcps weren't working.
	- Actions from mirc users in dcc chat was displayed in double.

v0.7.13 1999-07-21  Timo Sirainen <<EMAIL>> [unstable]

	* %p in text formats is changed to $, looks much cleaner :) Old
          formats in configuration files are automatically converted.

	* I got some documentation done! :) I wrote a list of all commands
	  irssi knows with (very) short descriptions, see COMMANDS file

	+ Hebrew support by Ronen Tzur <<EMAIL>> - see README-HEBREW
        + Users with gone-flag are displayed with different color in nicklist
          List is updated with USERHOST commands in small pieces every now and
          then..
        + Statusbar with some information in it ..
        + Away message is displayed differently in /whois and /msged people
          who's gone
        + /window goto <n>, /window prev, /window next
        + /window level [+/-]pub/msgs/...
            /window level msgs      - creates messages window
            /window level all -msgs - creates status window
	+ /bantype <normal/host/domain/custom>
	    - Normal - *!user@*.domain.net
	    - Host   - *!*@host.domain.net
	    - Domain - *!*@*.domain.net
	    - Custom [nick] [user] [host] [domain]
		eg. /bantype custom nick domain - nick!*@*.domain.net
		eg. /bantype custom user host - *!<EMAIL>
	+ /version - just displays version number..
        + You can use different font in each channel
	+ Alt-q..o changes channels to 11..19
        + Color configuration changes..
        + irssi-text : Reading manuals help ;) Text's backgound color isn't
	  changed to black anymore so pixmaps etc. should show up nicely :)
        + /notify nick!mask@* [ircnets], /unnotify
        + When trying to connect to server, you can abort it with the
	  cancel button in statusbar
	+ First parameter of /disconnect is now * (current server) or
          server tag
	+ You can now use !channels with their short names (not always)
        + Right clicking nick in channel pops up nicklist menu
        + You can select multiple nicks from nicklist and execute the command
          for all of them.
        + Panel applet supports panel size changes
	+ Window tabs have numbers now
	+ Ctrl-N changes to next window, Ctrl-P changes to previous window
	- Max. autoget size didn't work right, it got the file if the file was
	  bigger than the max. size.. and it was compared as bytes, not kB's.
	- Panel applet should now work right
        - Hilight words feature was completely broken
        - DCC Chats were displayed twice in status dialog
        - Closing DCC chat still had a few problems
        - After trying to join to channel where you could get in (invite only,
          banned, etc.) the created channel window wasn't destroyed.
        - configure didn't check -lnsl right..
	- Channel settings weren't read in the correct order -> autojoining
          to channels created the windows in reverse order every time.
        - ZVT in GNOME CVS broke irssi.. Fixed.
        - Quit message wasn't displayed if there was some commands waiting
          for transmit - quit was added to transmit queue and connection
          closed..
	- Matching irc masks (nick!host@address) was case-sensitive..

v0.7.12 1999-07-06  Timo Sirainen <<EMAIL>> [unstable]

        * #irssi is now started in IRCnet

	* Release #2 :) The next day..
           + configure checks for -lnsl too
           + changed default font to fixed size so I wouldn't have to hear all
             the time how screen is messed up with zvt :)
           - background color couldn't be changed with zvt
           - irssi-text sometimes crashed at startup because of uninitialized
             variable..
           - you had to use --without-gnome-panel even if you didn't build
	     fwith gnome

        + Colorless theme, should be easy to start a new theme with using
          this. You need to copy the [theme:colorless] section to
          ~/.irssi.conf (or ~/.gnome/irssi whatever you happen to use..) from
          included irssi.conf to use this..
        + You can DCC send and get files via DCC chat (don't need to be
          connected to server), don't know if this works with any other client
          or if any other client has this ability.. BitchX didn't seem to
          have.
        + /WALL [#channel] message - Send notice to all ops in channel
	+ /last [-pub -msgs etc..] <text> for text mode version
        + Text mode version statusbar: -- more --, away (zZzZ)
        + The "-!-" and "-!- Irssi:" texts and timestamp is now configurable
        + Channel windows aren't destroyed anymore after getting disconnected
        + /window close
        + Outgoing flood protection: all commands you send to server are are
          queued and sent every 2 seconds. (if queue is empty, the command is
          sent immediately)
        + Notify list popup dialogs are now optional
        + /unalias (you could already do this with /alias)
        + You can send Mirc style CTCPs now in DCC chat (preferences/dcc),
          also if mirc user first sends ctcp, it's automatically set to
          default for that dcc chat session.. You can also set it with
          /mircdcc [y|n] or select from menu.
        + Default color number in setup, this color is used if nothing else
          is specified.
        + Server reconnection - you can add several irc servers to setup with
          same ircnet and autoconnect set and irssi goes through that list
          every time server gets disconnected unintentionally.
        + irssi-text word splits the lines. also if it needs to split the line
          it leaves 10 empty spaces at the start of the next line.
        + --without-gnome-panel switch to configure
        - http://blah@a.b opened e-mail client instead of http client
        - I set the socket non-blocking AFTER connect(), argh! This caused
          irssi to hang when trying to dcc get from bogus IPs or something.
        - Background color wasn't read right
        - Log dialog had some bugs
	- Banning ip addresses didn't work right
        - Some DCC problems fixed..
	- Some irssi-text bugs fixed

v0.7.11 1999-06-06  Timo Sirainen <<EMAIL>> [unstable]

	* Because of the color system changes, remove the [colors]
          section from irssi's configuration file or you will get some weird
          colors..

          The colors are pretty much taken from BitchX, IMO it looks nice :)
          But somehow I think many of you don't like it, so I made theme
          selector. Just need to make a few default themes..

	+ Color system changed, the old one looked pretty ugly especially in
          text mode version.. You can now have more than one color/line by
          adding %fg[,bg] codes to text formats.. fg and bg are the normal
          0-15 colors, in GUI version (without ZVT) you can use up to 99
          user specified colors.
        + Theme selector
        + Text mode version: Entry line editing is working great! Command line
          history works, scrollback buffer works, statusbar is working (again,
          copying bitchx..), reads configuration file .. This is starting to
          become usable :)
        + ZVT working better: font can be changed, transparency and background
          pixmap can be changed on the fly, the ugly block cursor isn't
          displayed anymore and wordclicking (urls, etc.) works.
	+ New GUI dialogs for /list, /who, /gwhois and when receiving invites
          to channels.
        + Channels dialog changed a bit. New "Quick join" dialog where you can
          specify server and channel to join
        + Mirc DCC resumes are working. By Trever Adams
          (<EMAIL>)
        + List of text strings to hilight
        + Notify dialog is created when someone in notify list joins irc.
        + Nick completion improvements, /msg nick<tab> works and in channels
          you can complete nicks anywhere in the entry.
        + Window save size/position dialog
        + DCC send added to popup menus
        + Removing lines from GtkText is really slow, so now they're removed
          with several lines at a time. (default is 20)
        + /window new [hidden] creates new window, /window server <server tag>
          changes server in current channel, useful in text mode version..
        + You can try to find memory leaks with giving --with-memdebug switch
          to configure
        - Still some bugs with DCC SEND fixed..
        - DCC list dialog crashed if there were dcc chats open, it also caused
          some random crashes when running..
        - Maybe window size/position saving finally works right?
        - g_(s)list_remove_link() didn't work as I had always thought .. It
          moves the link into separate list and doesn't free memory used by
          it like I thought.. So, inserted a few g_list_free_1() calls.
	- When not using menubars, popup menu should have displayed all the
          items in it, it got broken in .10.
        - signal_add_after() didn't work right.. actually it had a wrong name
          too, changed to signal_add_first() and made it to run these events
          before the normal events. This makes ignoring work again.
        - /notice was buggy
        - Configuration file handling (GTK version) was still a bit buggy..
        - Lots of small bug fixes here and there..

v0.7.10 1999-05-09  Timo Sirainen <<EMAIL>> [unstable]

	* ALL KNOWN CRASHES FIXED !! Weird, after changing the code with
          creating channels to empty windows, GtkText widget started working
          again, it used to crash after running the test ircserver for a
          while.. Maybe a few more versions and I'll release a "stable"
          labeled version again (08.0).

	+ GNOME version can use ZVT widget to draw texts. This is a lot faster
          than GtkText and with it you can use nice non-scrolling backgrounds
          and transparency! However, you'll have to use the default colors
          with it for now and Window/Save Buffer or Find doesn't work in it.
          !!NOTE!! ZVT in gnome 1.0.9 is buggy, it sometimes crashes when
          destroying zvt widget (leaving channels). It should be fixed in next
          version (which doesn't currently exist..)
	+ DCC transfer dialog, display list of all going dcc transfers, the
	  old dialogs can also be created.
        + Channel specific background pixmaps, if you don't have Imlib you can
          use only .xpm images.
	+ /ban and /unban changed, they accept multiple arguments and channel
          name may be specified as the first argument
        + dcc actions from mirc should work now
        - Text widget size is now saved instead of the window's size, should
          work better.
        - Right clicking text widget created a popup menu, but select
          selection got broken after it
        - Some potential bugs fixed after got kicked from channel
        - Log dialog was buggy
        - If dcc chat was closed but the query window was still there, trying
          to chat again with the same nick created another query window but
          used the old one..
        - C-C, C-B, etc. add the character at the end of the entry, not at the
          current position
        - Redirecting commands was a bit buggy, it always expected to receive
          the specified events. This worked with ISON command, but I forgot
          that WHOIS could also return "no such nick" events.. So, notifylist
          should now work right instead of sometimes printing whois results.
        - You couldn't use the normal control-? keys (c-left, c-right,
          c-insert, ..)
        - mirc colors were displayed with wrong colors
	- Changed all isspace(), isdigit() and isalnum() calls to cast their
          argument as (gint) to remove warnings when compiling with IRIX.
	- Speech plugin wasn't working again..
        - After changing text format from setup, you couldn't change any
          other lines without closing setup dialog first
        - Font couldn't be changed by editing the entry line
	- /knockout format changed, it's now /knockout [timeout] nick reason
          (so the reason can have number at the start of it..), it also used
          to crash when unbanning
	- DCC send fixed, fast send didn't work and without it it was eating
          all cpu.
        - DCC sending files with spaces in their name didn't work (they're
          changed to _ now.)

v0.7.9 1999-04-22  Timo Sirainen <<EMAIL>> [unstable]

	+ Server/status dialog, displays list of all connected servers,
          channels, queries and dcc chats.
        + Host resolving is now done in a child processes. Hopefully works
          better than threads which aren't used anywhere anymore..
        + Window/Save window size and Save window position, next time the same
          window (status, msgs, channel, query) is opened the saved size
          and/or position will be used.
        + gui_widget_depends_data(), widget will be automatically destroyed
          if the specified signal is called with the specified argument. Used
          to destroy DCC request dialogs when they're closed manually, timed
          out or rejected at the other end..
        - Fixed lots of memory leaks which some might have caused crashes..
          src/memdebug.c has the debug functions I used ..
        - DCC CLOSE closed always the last dcc connection instead of the one
          that matched the parameters
        - Commented out all GUI_INPUT_EXCEPTIONs .. I don't even know when
          exceptions are sent and why (I thought that only when some error
          occurred..), Linux doesn't seem to send them ever? IRIX however sends
          them all the time which made irssi eating all cpu.
        - Fixed compiling gui-text with systems that had only slang/slang.h
        - gui_widget_depends() had some bugs
        - adding irc networks by typing it's name when adding server didn't
          update the GUI of ircnets list.
	- If some plugins were already loaded, and loading new plugin failed
          and it called plugin_deinit(), the call (might have) called some
          other plugin's plugin_deinit() and crash.. Some updated to change
          all global plugin functions to plugin_name_* calls so they wouldn't
          call other modules functions..
	- If day changed when logging, the log file contained the day change
          lines before each line after that..
	- Channel labels were hilighted even when the channel was selected..

v0.7.8 1999-04-12  Timo Sirainen <<EMAIL>> [unstable]

	* This version has lots of internal changes, I haven't tried them
          much so hopefully everything works right..

	+ external plugin, reads commands from named pipe and executes them
        + sample plugin updated, creating new plugins should when based on
          this one
        + gui-none is built, it tries to run bot plugin which doesn't exist..
	+ Moved the configuration code to the lowest level, made settings
          to be common with all guis.
        + /set command: /set [category[/key[=value]]] [all]
        + /alias, /ignore and /unignore commands work again
        + Nick completion finally working, and better than ever (unless it
          has bugs of course ;). It tries to be smart when completing, first
          it checks if someone with same letters had recently written message
          to you, then it checks for nicks that recently wrote messages and
          finally for the rest of the nicks in channel. Clicking tab more
          scans the list. Clicking tab in empty line completes to /msg <nick
          who sent you last message>. Before trying to complete nicks, tab
          completion checks if the word is in setup's completion list.
        + Wrote some functions to make possible handling events in different
          places depending where the command which created the event was sent.
          This is useful for example to notify list which needs to send ISON
          requests but user should still be able to use /ison command
          normally (this has always worked btw).
        + Notify list's notify event now displays user's host and real name
          (made with the previous functions which grabbed WHOIS). Notify list
          GUI also updated for this
        + You can use user/host masks with notify lists, wildcards are
          allowed. Nick must always be specified (could be fixed but everyone
          is invisible anyway so it would be useless). Examples:
          friend!*friend*@*, friend_!*friend*@*,
          another!*@*.blah.com, altnick!*@*.blah.com
	+ "your nick is owned by blah (<EMAIL>)" when connected
        + Text formats page uses now GtkText widget, it displays colors too.
          It's not perfect but better than before...
        + There's now three different colors indicating what's happened in
          channel: red = some text was written there, bright red = public
          message, magenta = public message to you
        + Ignore checking is now done by stopping the entire signal so
          plugins etc. don't have to deal with them.
        + You can use user/host masks with ignores
        + Autoraise (in window menu) window when new message comes to channel
        + Channel settings: You can specify list of bots (masks) and command
	  to send the first one found bot (nice for auto-opping ourself)
        - Changing user modes from menus didn't work. Moved the menu under
          server menu.
	- Speech plugin problems fixed.. It didn't compile without gnome
          libraries and with gnome libraries it didn't say anything because
          for some reason one line in sources was commented out..
        - IRC network things and server password didn't work because of stupid
          little bug
        - Configuration file handling was a bit broken in GTK version
	- Rewrote menu handling in GTK version, it was crashing when trying
          to load plugins..

v0.7.7 1999-04-05  Timo Sirainen <<EMAIL>> [unstable]

	+ speech plugin :) This is currently made to work with festival
          (http://www.cstr.ed.ac.uk/projects/festival/), it's not very usable
          but nice to play with :)
        + C-b (bold), C-c (color), C-g (bell), C-v (reverse), C-- (underline)
          keys work now but they don't display anything in entry line.
        + Beel character beeps are now optional
        + Fixes and new features for plugins. Each plugin has now it's own
          menu under plugin menu and "Unload" menuitem there by default.
        - GTK version didn't build .. again..
        - I broke DCC send in 0.7.5
        - SIGPIPE is ignored, maybe fixes some "crashes" when server
          connection is lost (I never got this btw.)
        - Using irssi with click to focus sent commands to window where mouse
          cursor was..
        - Fixed some bugs when scrollback buffer got full (GTK's text widget
          seems to crash sometimes with small scrollback buffer and _lots_ of
          text!)
        - Plugin dialog didn't show any plugins
	- Plugins are now created with automake things to make them portable
          for compilers/linkers that don't work with -shared switch. The
          disadvantage is that a lot of unnecessary files are created for
          each plugin (.a, .la, .so.0, .so.0.0, .so.0.0.0) while irssi uses
          only libplugin.so, could be fixed somehow but too difficult for me..
	- Joining to +k channel crashed

v0.7.6 1999-03-29  Timo Sirainen <<EMAIL>> [unstable]

	* New default colors .. I think they're better, not the best possible
          but anyway, I'm not good with these :)
        + Text mode version working again with colors! :)
	+ New settings/servers dialog, changed connect and channels dialogs.
          You can now automatically connect to multiple servers at startup.
          All this made by Kari Lavikka (<EMAIL>)
        + Server password support
        + IRC proxy support
        + Right clicking the channel name in panel pops up channel menu
        + private ctcp and notice messages now show up in query windows if
          it exists for sender
        - Fixed logging a bit, you can now log stuff from nicks without
          having query window for them.
        - If time stamps were enabled, log files had time stamps twice in each
          line
        - Color settings had some bugs
	- GNOME version crashed if ~/.irssi.conf didn't exist .. because I
          read the configuration file before gnome_init() which was necessary
          for "create gnome panel applet" option in setup -> removed it

v0.7.5 1999-03-17  Timo Sirainen <<EMAIL>> [unstable]

	* Text mode version is broken and isn't built.
        * Configuration file has changed quite a lot, might be better if you
          would just erase the old one.. Server configuration is also going
          to change soon..

        + Plugins are back! But unfortunately I can't get perl plugin to work.
          See TODO for more information.
        + I divided setup dialogs to separate windows and grouped the options
          to different frames so they actually make some sense now :) Also
          some new options:
        	- Create GNOME panel applet
                - Lots of DCC options, the page looked so lonely that I had
                  to put more options there :)
                    - get: autorename file if it exists (currently this is
                      done always..)
                    - autoget: max. file size to get
                    - fast send (don't wait for responses from the other
                      side, just keep sending the data..)
                    - upload path
                    - block size
                    - timeout
	+ Checks for slang/slang.h too ..
        + Text mode version doesn't have my name/nick hardcoded in it
          anymore :) Uses IRCNAME and IRCNICK environment variables.
        + Status window is now optional, when enabled it grabs all
          "status" messages there. This needed quite a lot of inner changes,
          hopefully works :)
        + /nctcp - send ctcp reply
        + Window size is now saved when quitting and it's used when creating
          windows in next session
        - Fixed lots of GLib-CRITICAL messages when disconnected from server
          while it was still trying to find ip address.
        - Window/find should now scroll to right position and it doesn't
          corrupt the text widget with inserting new texts to found texts
          positions.. It's also case-insensitive now.
	- Fixed compile error when building without gnome panel..

v0.7.4 1999-03-13  Timo Sirainen <<EMAIL>> [unstable]

	+ Applet for GNOME panel working again
	+ You don't have to specify --without-gnome anymore, configure checks
          this for you (finally)
        + Some widget packing changes and tooltips for channel mode buttons
          by Kari Lavikka <<EMAIL>>
        + Window/find moves the scrollbar to position where you can
          actually see the found text (usually, find prev misses it
          sometimes..)
        - Disconnect button in disconnect dialog still wasn't working.
        - Channel mode buttons didn't change if channel wasn't focused
        - I added separators to menus in v0.7.3, but forgot to make GTK
          version work with them
	- Closing DCC chat crashed
	- Destroying window with WM now really destroys the window, no hide
          it like it used to..
        - Several fixes with window handling..

v0.7.3 1999-03-11  Timo Sirainen <<EMAIL>> [unstable]

	* Text mode version is not called irssi-text so make install can
          install both of them without overwriting the other..
        + Window menu: find text, save window buffer, close window, new,
          new tabbed .. And lots of fixing code to make empty windows
          possible.
        + After kicked from channel, the window isn't destroyed
        - While connecting to server and receiving "nick is temporarily
          unavailable" irssi didn't try different nick but got just stuck
          there.

v0.7.2 1999-03-10  Timo Sirainen <<EMAIL>> [unstable]

	+ GUI for logging, /log start, /log stop
	+ You can drag file from GMC over to nick in nicklist to send the file
          with DCC
        + Nicklist changed to GtkCList, ops and voices are marked with
          pixmaps (stolen from X-Chat, someone want to do better ones?). Had
          to be done because adding drag'n'drop to GtkList was too slow..
	- Pretty bad bugs with GTK version fixed, using several dialogs'
          buttons crashed..
        - WHOIS's idle line displayed seconds wrong. Maybe I finally got it
          fixed right this time.. :)
	- Unknown commands still didn't work. They were sent to server with
          the / character at start..

v0.7.1 1999-03-08  Timo Sirainen <<EMAIL>> [unstable]

	* The 0.7.0 was actually quite stable, I thought it would crash all
          the time by itself and do some weird things :)
        * Hopefully now that GTK 1.2 is finished more people start using it
          and don't ask me to continue supporting GTK 1.0. So, this version
          requires GTK 1.1/1.2.

        + Started gui-text.. Should work somehow :) Prefers SLang but should
          work with curses also, except in X terminals I couldn't get colors
          out of it.
        + Logging, still lacks GUI.

          Syntax:
            /log create <filename> [<+/->level ...] [#channel/nick [
                                   [<+/->level ...] ...]
            /log close <filename>
            /log list

          Example: /log create mylog -all +msgs #linux +chan +public +notices
          (or simply #linux +all logs everything that appears in #linux
          window).

          You can use these levels, ALL is set by default:
          ALL, CRAP, PUBLIC, MSGS, NOTICES, WALLOPS, SNOTES, ACTIONS, DCC,
          CTCP, CLIENTNOTICES, CLIENTERRORS
	+ Automatically create query window when received msg (option)
	+ No more "WHO: unknown commmand"s, all unknown commands are sent to
          server just like they were written
	+ Working again: msgs window, aliases, /kickban, /knockout
        + Changed quite a lot of GString's to g_strdup_printf
        + cleaned configure.in
        - Disconnect button in disconnect dialog didn't work.
        - Writing to DCC chat only sent the first word..
        - setting ircnet to server record was done without strdup()ing from
          setup server record, so after disconnecting preferences or
          reconnecting could have crashed.

v0.7.0 1999-02-25  Timo Sirainen <<EMAIL>> [unstable]

	* Lots and lots and LOTS of rewriting code. Hope you like it :) At
          least I do, mostly. GUI still needs rewriting but.. well, it works
          anyway now.

          The [unstable] tag in this version really means that IT IS UNSTABLE.
          See TODO for what features that 0.6.0 have are still missing.

          Because of the new great signaling system :) lots of things can be
          done easier. Now all the dialogs should be up to date, like channel
          mode dialog. When you receive ops, widgets come sensitive, when you
          lose your ops, the widgets become unsensitive.

          irssi source is now also divided in to separate directories:

          irc-base: this shouldn't change much, it has all basic functionality
                    needed to create a working IRC client.

          irc-extra: all kinds of extra functionality: dcc, flood detection,
                     ignore lists, (logging soon), notify lists and plugins.

          common-ui: here's all the functions that need to print some texts to
                     screen.

          gui-gnome: GTK/GNOME specific code

          These haven't been started yet:
          ui-none: make irssi work as a bot with plugins/scripts
          ui-text: text mode interface
          gui-kde, ...: I'm not going to do these, anyone?

v0.6.0 1999-02-12  Timo Sirainen <<EMAIL>> [unstable]

	+ Plugins! See plugins/* and src/plugin-commands.h for more
          information
        + Small PERL plugin, anyone want to continue developing this?
        + Show menubar option
	- Some fixes and changes with clicking words.
        - Notify list didn't work if ircnet was specified.
        - socks5 needed -DSOCKS to work (but this is still untested..)

v0.5.1 1999-02-10  Timo Sirainen <<EMAIL>>

	* A big bugfix release, hopefully no crashes anymore? :)
	* Removed intl directory, we don't have any i18n support yet so it's
          not needed...

	+ Time stamps
        + Use --with-pthreads=no if they don't work right
        + socks4/5 support (untested), use --with-socks=no if you don't want
          them.
	- If you got kicked from channel irssi was in quite unstable state
	- Using channel mode buttons in the upper right corner crashed
	- Whois displayed idle time wrong
	- Adding first item to empty list (notify, completions, aliases,
          ignores) crashed.
	- It didn't actually compile without pthreads lib..
	- If any texts contained %s, %d, etc.. irssi tried to expand them
	- Solaris (and probably some others) need -D_REENTRANT flag to make
	  threads work corretly.
	- gtk_container_add() should be used instead of
          gtk_scrolled_window_add_with_viewport() when handling clists..

v0.5.0 1999-02-08  Timo Sirainen <<EMAIL>>

	+ DCC CHAT, SEND, GET
	+ pthread support, no more blocking server connections.
	+ Notify list
	+ Getting more paranoid :) Added a lot of g_return_if_fail()s.
          Hopefully not in wrong places :) But these surely save some crashes
          with buggy code..
        + BUILD_DOCK, HAVE_GNOME, etc. defines are now placed in config.h
          instead of being in -D arguments for gcc.
        + Format strings are more flexible now, you can change the order of
          the parameters and you don't need to specify if the argument is
          supposed to be string or integer or .. Should be easy to use, %p1
          matches the first argument, %p2 the seconds, etc.
        + /PING
        - Changing topic from topic entry widget didn't work.
        - If window had only one channel, the channel widgets (topic, modes,
          etc) didn't show up.
	- Using popup menu from status window's channel lists crashed.
        - Channel menu didn't work

v0.4.1 1999-02-04  Timo Sirainen <<EMAIL>>

	+ Preferences: completion - if you type <tag> in entry field and
          press tab it gets completed, like setting homepage to http://blah
          and typing: home page is homepage<tab> -> home page is http://blah
	+ Tab nick completion.
        + ':' nick completion also changed a bit.. If it fits to more than
          one nick it's completed in bash style. Like if there's mynick1 and
          mynik2, "my:" gets completed to "myni:"
        + glib 1.0.6 didn't have g_get_user_name(), g_get_real_name(),
          g_get_home_dir() and g_strncasecmp(), made them..
        - Using --no-applet crashed..
	- Using items in user modes menu crashed
        - Several small bugs fixed..

v0.4.0 1999-02-01  Timo Sirainen <<EMAIL>> [unstable]

	+ *Lots* of internal changes with window handling, fixed some memory
          leaks also while doing them. You can now have multiple real windows
          with tabbed subwindows in them. It's also possible to have multiple
          channels in one subwindow. Commands for handling these.. :
           - /join - Works like before, creates real or subwindow depending
             if "use tabbed windows" is set in setup.
           - /wjoin - Joins channel to current window
           - /hjoin - Creates new subwindow and joins there
           - /njoin - Creates new real window and join there
        + Changed URL max length from 20 to 200.. Didn't realize it was that
          low :)
        + Shows the nick who sent wallop, you need to change the second
          line in format texts from "%s" to "%s: %s" to make this it work.
          Need to do some kind of autoupgrading for formats that change..
	+ Found new functions from glib :)
            - g_getenv("HOME") -> g_get_home_dir()
            - getpw() -> g_get_user_name() and g_get_real_name()
            - strcasecmp() -> g_strcasecmp()
            - strncasecmp() -> g_strncasecmp()
        + GNOME version uses ~/.irssi.conf if it exists
        - Opping/deopping anyone made irssi think that you were opped/deopped
        - read_line() had some pretty bad bugs...

v0.3.6 1999-01-29  Timo Sirainen <<EMAIL>> [unstable]

	+ irssi.spec to easily build .rpm
	+ Preferences:
           - Alternative nick which is used when default nick is in use.
           - Create own window for msgs
           - Tab orientation
           - Flood settings
           - Max lines to keep in command line history
           - Scrollback buffer size
           - Text formats
        + Toolbar .. we need pixmaps .. Needs also window (channel/query)
          specific items.
        + Using horizontal panel works right now.
        + Alt-1..0 changes between windows
        - Private actions didn't show up in query windows where they should
          have.
        - Alias and ignores lists were mixed together and didn't work.
        - Setting max channels to display in panel to -1 (which is default..)
          displayed actually only one channel.. Also setting this to 0 works.
        - Topic didn't change when changing between windows in tabbed windows
          mode.
	- When op received +v, @ was changed to + in nick list
	- Connect/disconnect/channels dialogs fixed so that they won't crash
          when clicking buttons with empty lists.

v0.3.5 1999-01-26  Timo Sirainen <<EMAIL>>

	+ Finished the channels dialog, you can automatically join to
          channels in specific irc networks.
        + Changed the look of connect and disconnect dialogs
	+ servertest/ directory, just a test program to try if irssi crashes
          with _HEAVY_ network load (ie. if there's buffer overflows or some
          other weird bugs). It doesn't :)
        + Preferences: Maximum number of channels to display in panel
        - When leaving from channels, panel didn't redraw it's list correctly
	- Leaving channels in tabbed window mode crashed
	- Fixed crash if connection got lost

v0.3.4 1999-01-24  Timo Sirainen <<EMAIL>> [unstable]

	+ Tabbed windows work a lot better
        + User mode menu
        + Preferences: default user mode
        - Connecting to more than one server crashed...
	- Nick list redrawing was broken
	- Dock applet wrote the texts to pixmap but didn't draw the pixmap
          into screen then properly..

v0.3.3 1999-01-23  Timo Sirainen <<EMAIL>> [unstable]

	+ /ignore never - never autoignore nick
        + You can hide/show channel nick list from Channel menu, default
          state can be set from preferences.
        + Preferences: Strip styles from text, misc options
        + Launching URLs work!
        + More str[nnn] -> GString changes, should be no more potential
          buffer overflows
        + Started the tabbed windows, probably quite buggy and the
          window_create() code is getting REALLY ugly..
        - Servers didn't display QUIT message.. Couldn't think of any better
          way to fix this than not to disconnect the link but let the server
          do it.
        - ANSI colors didn't work right

v0.3.2 1999-01-22  Timo Sirainen <<EMAIL>> [unstable]

	+ Dock applet, works at least with Enlightenment..
        - GTK version tried to move temp config file to real config file
          with rename() .. didn't work if /tmp and home were in different
          partitions.
	- Some servers sent a mode change before /names list, irssi didn't
          like that and crashed..
        - No more Gtk-Critical messages if irssi is run with --no-applet

v0.3.1 1999-01-22  Timo Sirainen <<EMAIL>>

	* 4 days since last release. too long :) I'm now starting to create
          "unstable" versions of irssi. They have the latest and greatest
          features while they might not build/work too well. Check
          http://www.sicom.fi/~ikioma/irssi-download.html, new versions will
          probably be released quite often.
	* GNOME version now builds without GNOME panel applet library
        * Works with GTK+ 1.0.6 now, maybe with older too.
        + Connect / disconnect dialogs, channel dialog also started
        + Server setup dialog changed some.
        + Status window has a list of channels, queries and (yet not
          implemented) DCC chats. Also the tiny panel window isn't displayed
          unless you're running irssi in panel..
        + Menu bar in all windows
	+ Cleaned read_line() to use GStrings.
        + $(sysconfdir)/irssi.conf is copied to default user file if it isn't
          found.
        + If you get kicked from channel the channel window won't get
          destroyed.
        - Query was in op submenu in nicklist's popup menu .. whops.
        - 0.3.0 broke server tag generation so using multiple servers didn't
          work.

v0.3.0 1999-01-18  Timo Sirainen <<EMAIL>>

	* Config changes in GTK version, delete old .irssi.conf file (or
          change all "tag = values" to "tag=values")
        * Default set of servers and aliases can be found from irssi.conf,
          copy that to ~/.gnome/irssi (if build with GNOME) or ~/.irssi.conf
          (if build without GNOME).
        + servers page added to preferences. Without connect dialog this is
          quite useless though :) But if you set "connect to IRC server at
          startup" on, irssi connects you to first local server.
	+ aliases :
	   - /ALIAS <alias> <command to execute>
           - alias page added to preferences
           - these codes are extracted in commands:
               %0            : alias name
               %1, %2, %3 .. : word %
               &1, &2, &3 .. : word & + the rest of the text after it
               %c            : channel name
           - typing extra / before /command (//command) ignores any aliases
        + ignore list :
           - /IGNORE <mask> <ignore level>
           - /UNIGNORE <mask> <ignore level>
           - ignore page added to preferences
           - ignore levels: ALL, CRAP, CHAN, PUBLIC, MSGS, NOTICES, WALLOPS,
             SNOTES, ACTIONS, DCC, CTCP, CLIENTNOTICES, CLIENTERRORS
        + autoignoring msg and ctcp flooders
        + options page added to preferences
        + invite lists (channel mode I)
        + !channels should work now
        + replaced quite a lot of g_new()'s with GStrings. fixed one buffer
          overflow with this also..
        + /AWAYALL - sends /away to all connected servers
        + /KNOCKOUT [secs] <nick> <reason> - kick+ban+delay (5min)+unban
        - nick completion was case-sensitive
        - again some minor bugs fixed and features added

v0.2.1 1999-01-17  Timo Sirainen <<EMAIL>>

	+ Preferences: color and font selection
        + gnome-stuff.c has some gnome_* compatible functions to get GTK+
          version work. They're pretty slow and dum and maybe even buggy so
          if you want better, just compile the gnome libs :)
        + Doubleclicking in topic sets the entry editable/uneditable
	- nick completion was buggy
        - some minor bugs and features fixed

v0.2.0 1999-01-16  Timo Sirainen <<EMAIL>>

	+ CTCP VERSION returns system name and revisions
        + msgs window has now autoraise set on as default
        + status window is used only when there's no window active..
        + Done server handing:
           /server = /disconnect + /connect
           /connect = connects to new server without disconnecting from
                      any old ones
           /disconnect = disconnect from current server
        + msgs and status window have a server selector menu
        + clicking a server tag in msgs window changes server
        + channel information box
        + --no-panel command line switch so you can build with GNOME support
          but don't need to be running it in panel.
	- some automake fixes
	- If someone was kicked, the kicker was removed from nick list
          insted of the kicked..
        - Fixed some weird situtation where snapshot window wouldn't
          disappear from screen..

v0.1.0 1999-01-14  Timo Sirainen <<EMAIL>>

	* First release
