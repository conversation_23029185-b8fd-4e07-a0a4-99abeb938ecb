TYPEMAP
Irssi::Chatnet		T_IrssiObj
Irssi::Server		T_IrssiObj
Irssi::Connect		T_IrssiObj
Irssi::Reconnect	T_PlainObj
Irssi::Channel		T_IrssiObj
Irssi::Query		T_IrssiObj
Irssi::Command		T_PlainObj
Irssi::Nick		T_IrssiObj
Irssi::Ignore		T_PlainObj
Irssi::Log		T_PlainObj
Irssi::Logitem       	T_PlainObj
Irssi::Rawlog		T_PlainObj
Irssi::Module		T_PlainObj
Irssi::Windowitem	T_IrssiObj

INPUT

T_IrssiObj
	$var = irssi_ref_object($arg)

T_PlainObj
	$var = irssi_ref_object($arg)

OUTPUT

T_IrssiObj
	$arg = iobject_bless((SERVER_REC *)$var);

T_PlainObj
	$arg = plain_bless($var, \"$ntype\");

