#include <irssi/src/perl/common/module.h>

#include <irssi/src/fe-common/core/fe-windows.h>
#include <irssi/src/fe-common/core/fe-exec.h>
#include <irssi/src/fe-common/core/formats.h>
#include <irssi/src/fe-common/core/printtext.h>
#include <irssi/src/fe-common/core/window-items.h>
#include <irssi/src/fe-common/core/themes.h>
#include <irssi/src/fe-common/core/keyboard.h>

typedef WINDOW_REC *Irssi__UI__Window;
typedef TEXT_DEST_REC *Irssi__UI__TextDest;
typedef THEME_REC *Irssi__UI__Theme;
typedef KEYINFO_REC *Irssi__UI__Keyinfo;
typedef LINE_INFO_META_REC *Irssi__UI__LineInfoMeta;
