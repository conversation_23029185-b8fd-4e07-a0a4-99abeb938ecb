#include <irssi/src/fe-common/core/formats.h>

/* clang-format off */
enum {
	IRCTXT_MODULE_NAME,

	IRCTXT_FILL_1,

	IRCTXT_NETSPLIT,
	IRCTXT_NETSPLIT_MORE,
	IRCTXT_NETSPLIT_JOIN,
	IRCTXT_NETSPLIT_JOIN_MORE,
	IRCTXT_NO_NETSPLITS,
	IRCTXT_NETSPLITS_HEADER,
	IRCTXT_NETSPLITS_LINE,
	IRCTXT_NETSPLITS_FOOTER,
	IRCTXT_NETWORK_ADDED,
	IRCTXT_NETWORK_REMOVED,
	IRCTXT_NETWORK_NOT_FOUND,
	IRCTXT_NETWORK_HEADER,
	IRCTXT_NETWORK_LINE,
	IRCTXT_NETWORK_FOOTER,
	IRCTXT_SETUPSERVER_HEADER,
	IRCTXT_SETUPSERVER_LINE,
	IRCTXT_SETUPSERVER_FOOTER,
	IRCTXT_SERVER_WAITING_CAP_LS,
	IRCTXT_SASL_SUCCESS,
	IRCTXT_SASL_ERROR,
	IRCTXT_CAP_REQ,
	IRCTXT_CAP_LS,
	IRCTXT_CAP_ACK,
	IRCTXT_CAP_NAK,
	IRCTXT_CAP_LIST,
	IRCTXT_CAP_NEW,
	IRCTXT_CAP_DEL,

	IRCTXT_FILL_2,

	IRCTXT_JOINERROR_TOOMANY,
	IRCTXT_JOINERROR_FULL,
	IRCTXT_JOINERROR_INVITE,
	IRCTXT_JOINERROR_BANNED,
	IRCTXT_JOINERROR_BAD_KEY,
	IRCTXT_JOINERROR_BAD_MASK,
	IRCTXT_JOINERROR_SECURE_ONLY,
	IRCTXT_JOINERROR_UNAVAIL,
	IRCTXT_JOINERROR_DUPLICATE,
	IRCTXT_CHANNEL_REJOIN,
	IRCTXT_INVITING,
	IRCTXT_CHANNEL_CREATED,
	IRCTXT_CHANNEL_URL,
	IRCTXT_TOPIC,
	IRCTXT_NO_TOPIC,
	IRCTXT_TOPIC_INFO,
	IRCTXT_CHANMODE_CHANGE,
	IRCTXT_SERVER_CHANMODE_CHANGE,
	IRCTXT_CHANNEL_MODE,
	IRCTXT_BANTYPE,
	IRCTXT_NO_BANS,
	IRCTXT_BANLIST,
	IRCTXT_BANLIST_LONG,
	IRCTXT_QUIETLIST,
	IRCTXT_QUIETLIST_LONG,
	IRCTXT_EBANLIST,
	IRCTXT_EBANLIST_LONG,
	IRCTXT_NO_INVITELIST,
	IRCTXT_INVITELIST,
	IRCTXT_INVITELIST_LONG,
	IRCTXT_NO_SUCH_CHANNEL,
	IRCTXT_CHANNEL_SYNCED,
	IRCTXT_SERVER_HELP_START,
	IRCTXT_SERVER_HELP_TXT,
	IRCTXT_SERVER_END_OF_HELP,

	IRCTXT_FILL_4,

	IRCTXT_USERMODE_CHANGE,
	IRCTXT_USER_MODE,
	IRCTXT_AWAY,
	IRCTXT_UNAWAY,
	IRCTXT_NICK_AWAY,
	IRCTXT_NO_SUCH_NICK,
	IRCTXT_NICK_IN_USE,
	IRCTXT_NICK_UNAVAILABLE,
	IRCTXT_YOUR_NICK_OWNED,

	IRCTXT_FILL_5,

	IRCTXT_WHOIS,
	IRCTXT_WHOWAS,
	IRCTXT_WHOIS_IDLE,
	IRCTXT_WHOIS_IDLE_SIGNON,
	IRCTXT_WHOIS_SERVER,
	IRCTXT_WHOIS_OPER,
	IRCTXT_WHOIS_MODES,
	IRCTXT_WHOIS_REALHOST,
	IRCTXT_WHOIS_USERMODE,
	IRCTXT_WHOIS_CHANNELS,
	IRCTXT_WHOIS_AWAY,
	IRCTXT_WHOIS_SPECIAL,
	IRCTXT_WHOIS_EXTRA,
	IRCTXT_END_OF_WHOIS,
	IRCTXT_END_OF_WHOWAS,
	IRCTXT_WHOIS_NOT_FOUND,
	IRCTXT_WHO,
	IRCTXT_END_OF_WHO,

	IRCTXT_FILL_6,

	IRCTXT_OWN_NOTICE,
	IRCTXT_OWN_ACTION,
	IRCTXT_OWN_ACTION_TARGET,
	IRCTXT_OWN_CTCP,

	IRCTXT_FILL_7,

	IRCTXT_NOTICE_SERVER,
	IRCTXT_NOTICE_PUBLIC,
	IRCTXT_NOTICE_PRIVATE,
	IRCTXT_ACTION_PRIVATE,
	IRCTXT_ACTION_PRIVATE_QUERY,
	IRCTXT_ACTION_PUBLIC,
	IRCTXT_ACTION_PUBLIC_CHANNEL,

	IRCTXT_FILL_8,

	IRCTXT_CTCP_REPLY,
	IRCTXT_CTCP_REPLY_CHANNEL,
	IRCTXT_CTCP_PING_REPLY,
	IRCTXT_CTCP_REQUESTED,
	IRCTXT_CTCP_REQUESTED_UNKNOWN,

	IRCTXT_FILL_10,

	IRCTXT_ONLINE,
	IRCTXT_PONG,
	IRCTXT_WALLOPS,
	IRCTXT_ACTION_WALLOPS,
	IRCTXT_KILL,
	IRCTXT_KILL_SERVER,
	IRCTXT_ERROR,
	IRCTXT_UNKNOWN_MODE,
	IRCTXT_DEFAULT_EVENT,
	IRCTXT_DEFAULT_EVENT_SERVER,

	IRCTXT_FILL_11,

	IRCTXT_SILENCED,
	IRCTXT_UNSILENCED,
	IRCTXT_SILENCE_LINE,
	IRCTXT_ASK_OPER_PASS,
	IRCTXT_ACCEPT_LIST
};
/* clang-format on */

extern FORMAT_REC fecommon_irc_formats[];
