# Specyfikacja implementacji natywnych paneli w Irssi

## Etap 1: Zbadanie kodu źródłowego

**NAJPIERW** należy przeprowadzić głęboką analizę kodu Irssi w celu znalezienia optymalnego sposobu implementacji:

### 1.1 Zbadanie istniejących implementacji
```bash
git checkout origin/feature/sidepanels-native
# Sprawdź jak działa resize i reakcja na klik w tej gałęzi - to jedyna działająca implementacja
```

**Uwaga**: W gałęzi `[origin/feature/sidepanels-native]` resize działa poprawnie, pojawiają się dane w panelach i jest reakcja na klik.

### 1.2 Analiza architektury okien Irssi
Zbadaj następujące komponenty:

#### Główny system okien:
- `src/fe-text/mainwindows.c` - zarządzanie głównymi oknami
- `src/fe-text/gui-windows.c` - GUI okien 
- `src/fe-text/term.c` - obsługa terminala
- `src/fe-common/core/windows-layout.c` - układy okien

#### System event'ów i resize:
- `src/core/signals.c` - system sygnałów
- `src/fe-text/term.c:terminal_resize()` - obsługa resize terminala
- `mainwindow_resize()` functions - jak okna reagują na zmiany

#### Istniejące split windows:
- `src/fe-text/gui-windows.c` - mechanizmy splitowania
- Look for: `WINDOW_REC`, `MAIN_WINDOW_REC`, window splitting logic

### 1.3 Określenie strategii implementacji

**Opcja A - Natywna integracja (preferowana)**:
- Zintegruj panele z głównym systemem `MAIN_WINDOW_REC`
- Wykorzystaj istniejące mechanizmy resize i layout
- Panele jako specjalne typy `WINDOW_REC`

**Opcja B - Moduł**:
- Stwórz osobny moduł `src/fe-text/panels/` 
- Hook do istniejących sygnałów resize/layout
- Własne zarządzanie geometrią

## Etap 2: Implementacja natywnego układu 3-kolumnowego

### 2.1 Cel: Layout jak WeeChat
```
┌─────────┬──────────────────────────┬─────────────┐
│ Channels│      Main Content        │  Nicklist   │
│         │                          │             │
│ #chan1  │  [20:47] <nick> message  │ @operator   │
│ #chan2  │  [20:47] <you> response  │ +voice      │
│ query1  │                          │  normal     │
└─────────┴──────────────────────────┴─────────────┘
```

### 2.2 Kluczowe wymagania
- **Natywna integracja** z systemem okien Irssi
- **Automatyczny resize** - panele skalują się z głównym oknem
- **Stabilne UI** - brak rozsypywania się przy zmianie rozmiaru
- **Interaktywność** - klik na element = przełączenie okna/otwarcie query
- **Responsywność** - płynna reakcja na dane i zdarzenia

### 2.3 Implementacja bazująca na istniejącym kodzie

Po analizie `origin/feature/sidepanels-native`:

#### A) Wykorzystaj działające mechanizmy resize
```c
// Z log.txt widzimy, że resize działa poprawnie:
// [20:47:32] === TERMINAL RESIZED ===
// [20:47:32] moving existing term_win from (247,1) 15x75 to (240,1) 15x75
```

#### B) Skopiuj kluczowe elementy z working branch
- Mouse handling i SGR parsing
- Panel geometry calculations  
- Integration z `mainwindow_update_panels()`

#### C) Rozszerz o missing features
- Pełną obsługę danych (channels, nicklist)
- Themed rendering
- Configuration persistence

## Etap 3: Techniczne szczegóły implementacji

### 3.1 Struktura paneli
```c
// Extend istniejące MAIN_WINDOW_REC lub utwórz nowe
typedef struct {
    WINDOW_REC *window;
    MAIN_WINDOW_REC *main_window;
    PanelType type;              // CHANNELS, NICKLIST
    PanelPosition position;      // LEFT, RIGHT  
    int width, min_width, max_width;
    gboolean visible, active;
    GSList *items;              // Lista elementów w panelu
} PANEL_REC;
```

### 3.2 Integracja z głównym oknem
```c
// Modify MAIN_WINDOW_REC to support panels
struct _MAIN_WINDOW_REC {
    // ... existing fields
    PANEL_REC *left_panel;
    PANEL_REC *right_panel;
    int panel_left_width, panel_right_width;
};
```

### 3.3 Event handling
- Hook `"terminal resized"` signal 
- Update panel geometry automatically
- Preserve main content area calculations

## Etap 4: Git branches do sprawdzenia

Zbadaj wszystkie dostępne gałęzie:
```bash
git branch -r | grep -E "(panel|split|sidebar|layout)"

# Szczególnie:
git checkout origin/feature/sidepanels-native  # <- WORKING resize!
git checkout origin/cursor/implement-native-weechat-like-ui-layout-ce31
git checkout origin/feature/local/native-split-panels
```

Przeanalizuj różnice między nimi:
```bash
git log --oneline origin/feature/sidepanels-native ^master
git diff master..origin/feature/sidepanels-native
```

## Kluczowe zasady implementacji

1. **Wykorzystaj to co działa** - `origin/feature/sidepanels-native` ma working resize
2. **Integruj natywnie** - nie twórz zewnętrznego systemu zarządzania oknami
3. **Testuj incremental** - małe zmiany, częste testy resize
4. **Follow Irssi patterns** - używaj istniejących konwencji kodowania
5. **Preserve compatibility** - nie łam istniejących funkcji

Panele mają działać jak główne okno - reagować na resize, klik, dane - bez rozpadania się UI. Kluczem jest zrozumienie i wykorzystanie mechanizmów z working branch `sidepanels-native`.