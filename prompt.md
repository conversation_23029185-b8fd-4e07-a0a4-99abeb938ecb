# Specyfikacja implementacji natywnych paneli w Irssi

## Etap 1: 

### 1.1 Zbadanie istniejących implementacji


**Uwaga**: W ga<PERSON><PERSON><PERSON> `[origin/feature/sidepanels-native]` resize działa poprawnie ale to tylko próba implementacji i reszta tam nie działa, nie pojawiają się dane w panelach więc nie można dalej testować i nie wiadomo czy użyty tam flow w ogóle jest "dobrym" podejściem. Ty to oceń znając kompletny native flow dla tui

### 1.2 Analiza architektury okien Irssi
Zbadaj następujące komponenty:

#### Główny system okien:
- `src/fe-text/mainwindows.c` - zarządzanie głównymi oknami
- `src/fe-text/gui-windows.c` - GUI okien 
- `src/fe-text/term.c` - obsługa terminala
- `src/fe-common/core/windows-layout.c` - układy okien

#### System event'ów i resize i ogólnie kompletne ui.
- `src/core/signals.c` - system sygnałów
- `src/fe-text/term.c:terminal_resize()` - obsługa resize terminala
- `mainwindow_resize()` functions - jak okna reagują na zmiany

# Cel: Layout jak WeeChat
```
┌─────────┬──────────────────────────┬─────────────┐
│ Channels│      Main Content        │  Nicklist   │
│         │                          │             │
│ #chan1  │  [20:47] <nick> message  │ @operator   │
│ #chan2  │  [20:47] <you> response  │ +voice      │
│ query1  │                          │  normal     │
└─────────┴──────────────────────────┴─────────────┘
```

### 2.2 Kluczowe wymagania
- **Natywna integracja** z systemem okien Irssi
- **Automatyczny resize** - panele skalują się z głównym oknem
- **Stabilne UI** - brak rozsypywania się przy zmianie rozmiaru
- **Interaktywność** - klik na element = przełączenie okna/otwarcie query
- **Responsywność** - płynna reakcja na dane i zdarzenia

### 2.3 Implementacja bazująca na istniejącym kodzie

#### A) Wykorzystaj działające mechanizmy resize jeśli są poprawne

#### B) Rozszerz o missing features
- Obsługa myszy w panelach sig_gui_key_pressed może okazać się tu pomocne. (zweryfikuj!)
  klik na element ma po prostu otwierać okno klikniętego kanału lub query jak to ma miejsce w irssi.
- Pełną obsługę danych (channels, nicklist)
- Themed rendering
- Configuration persistence

## Etap 3: Techniczne szczegóły implementacji


## Kluczowe zasady implementacji

1. **Wykorzystaj to co działa, oceń czy da się podzielić main window na 3 kolumny czy raczej trzeba dokleić do niego z każdej strony coś na sztywno** 
2. **Integruj natywnie** - nie twórz zewnętrznego systemu zarządzania oknami - jeśli to możliwe
3. **Testuj incremental** - małe zmiany, częste testy resize
4. **Follow Irssi patterns** - używaj istniejących konwencji kodowania
5. **Preserve compatibility** - nie łam istniejących funkcji
6. 
Panele mają działać jak główne okno - reagować na resize, klik, dane - bez rozpadania się UI, ja w weechat.