
%9Syntax:%9

WINDOW LOG %|on|off|toggle [<filename>]
WINDOW LOGFILE %|<file>
WINDOW NEW %|[HIDDEN|SPLIT|-right SPLIT]
WINDOW CLOSE %|[<first> [<last>]]
WINDOW REFNUM %|<number>
WINDOW GOTO %|active|<number>|<name>
WINDOW NEXT%|
WINDOW LAST%|
WINDOW PREVIOUS%|
WINDOW LEVEL %|[<levels>]
WINDOW IMMORTAL %|on|off|toggle
WINDOW SERVER %|[-sticky | -unsticky] <tag>
WINDOW ITEM PREV%|
WINDOW ITEM NEXT%|
WINDOW ITEM GOTO %|<number>|<name>
WINDOW ITEM MOVE %|<number>|<name>
WINDOW NUMBER %|[-sticky] <number>
WINDOW NAME %|<name>
WINDOW HISTORY %|[-clear] <name>
WINDOW MOVE PREV%|
WINDOW MOVE NEXT%|
WINDOW MOVE FIRST%|
WINDOW MOVE LAST%|
WINDOW MOVE %|<number>|<direction>
WINDOW LIST%|
WINDOW THEME %|[-delete] [<name>]
WINDOW HIDE %|[<number>|<name>]
WINDOW SHOW %|[-right] <number>|<name>
WINDOW GROW %|[-right] [<lines>|<columns>]
WINDOW SHRINK %|[-right] [<lines>|<columns>]
WINDOW SIZE %|[-right] <lines>|<columns>
WINDOW BALANCE %|[-right]
WINDOW UP %|[-directional]
WINDOW DOWN %|[-directional]
WINDOW LEFT %|[-directional]
WINDOW RIGHT %|[-directional]
WINDOW STICK %|[<ref#>] [ON|OFF]
WINDOW MOVE LEFT %|[-directional]
WINDOW MOVE RIGHT %|[-directional]
WINDOW MOVE UP %|[-directional]
WINDOW MOVE DOWN %|[-directional]
WINDOW HIDELEVEL %|[<levels>]

%9Parameters:%9

    LOG:          %|Turn on or off logging of the active window, optionally specifying the log file to use.
    LOGFILE:      %|Sets the location of the log file to use for window logging without starting to log.
    NEW:          %|Creates a new hidden or split window.
    CLOSE:        %|Closes the current window, the specified one or all windows in the given range.
    REFNUM:       %|Go to the window with the given number.
    GOTO:         %|Go to the window with activity, with the given nickname, channel or with the specified number.
    NEXT:         %|Go to the next window numerically.
    LAST:         %|Go to the previously active window.
    PREVIOUS:     %|Go to the previous window numerically.
    LEVEL:        %|Changes the text levels to display in the window, or query the current level.
    IMMORTAL:     %|Modifies or queries the window mortality status. Immortal windows have an extra protection against WINDOW CLOSE.
    SERVER:       %|Change the active server of the window or the server stickyness. If the server is sticky, it cannot be cycled with next_window_item/previous_window_item
    ITEM PREV:    %|Make the previous item in this window active.
    ITEM NEXT:    %|Make the next item in this window active.
    ITEM GOTO:    %|Change to the query with the specified nickname, channel with the given name or window item number.
    ITEM MOVE:    %|Move the active window item to another window, or move the channel or query item specified by their name to the current window.
    NUMBER:       %|Change the active window number to the specified number, swapping the window already in that place if required. With -sticky, protect the window number from renumbering done by windows_auto_renumber. (To re-set the sticky attribute, use WINDOW NUMBER again without -sticky.)
    NAME:         %|Change or clear the window name. Window names must be unique.
    HISTORY:      %|Set or clear a specific named history to use for this window. All windows with the same named history will share a history.
    MOVE PREV:    %|Move the window to the place of the numerically previous window. At the first position, move the window to the end and renumber the consecutive block that it was part of.
    MOVE NEXT:    %|Move the window to the place of the numerically next window. At the last position, move the window to the first position and renumber the consecutive block at first position (if any)
    MOVE FIRST:   %|Move the window to the first position. Any windows inbetween are moved to their numerically next positions.
    MOVE LAST:    %|Move the window to the last position. Any windows inbetween are moved to their numerically previous positions.
    MOVE:         %|Move the window to the specified number or the first number that is in use when moving the window in the direction of the specified position. Any windows inbetween are shifted towards the old position of the window (unused positions remain empty)
    LIST:         %|List all the windows.
    THEME:        %|Applies or removes a per-window theme.
    GROW:         %|Increase the size of the active split window by the specified number of lines.
    SHRINK:       %|Decrease the size of the active split window by the specified number of lines.
    SIZE:         %|Set the current split window size to the specified number of lines.
    BALANCE:      %|Balance the heights of all split windows.
    HIDE:         %|Hides the current split window, or the split window specified by number or item name.
    SHOW:         %|Show the window specified by number or item name as a new split windows. It is made sticky when autostick_split_windows is turned on.
    UP:           %|Set the split window left or above the current one active. At the top, wraps to the bottom.
    DOWN:         %|Set the split window right or below the current one active. At the bottom, wraps left.
    LEFT:         %|Go to the previous window numerically that is part of the current sticky group (or not part of any sticky group).
    RIGHT:        %|Go to the next window numerically that is part of the current sticky group (or not part of any sticky group).
    STICK:        %|Make the currently active window sticky, or stick the window specified by number to the currently visible split window. Or turn off stickyness of the currently active window or the window specified by number.
    HIDELEVEL:    %|Changes the levels of text lines that should be hidden from view, or query the current hidden level.
    MOVE LEFT:    %|Move the window to the numerically previous location inside the current sticky group.
    MOVE RIGHT:   %|Move the window to the numerically next location inside the current sticky group.
    MOVE UP:      %|Move the current window to the sticky group of the previous split window. If no sticky group remains, the split window collapses.
    MOVE DOWN:    %|Move the current window to the sticky group of the next split window. If no sticky group remains, the split window collapses.

    -right:       %|Makes the command work on the width instead of height, or create the split window to the right instead of top.
    -directional: %|Set the split window in the given direction to the current one active, or move the current window to the sticky group of the split window in the given direction. (If no sticky group remains, the split window collapses.)

   %|Add the required arguments for the given command. Without arguments, the details (size, immortality, levels, server, name and sticky group) of the currently active window are displayed. If used with a number as argument, same as WINDOW REFNUM.

   %|LEVEL and HIDELEVEL modify the currently set level. Without arguments, the current level is displayed. Levels listed starting with `+' are added to the current levels. Levels listed starting with `-' are removed from the current levels. To clear the levels, start the new level setting with `NONE'. Levels listed starting with `^' are either removed or added from the current setting, depending on whether they were previously set or not (since Irssi 1.4.4). Levels listed as is are also added to the current levels. Afterwards, the new level setting is displayed.

%9Description:%9

    Manipulates the window layout and positioning attributes.

%9Examples:%9

    /WINDOW CLOSE
    /WINDOW ITEM MOVE 10
    /WINDOW GOTO 15
    /WINDOW GOTO ACTIVE
    /WINDOW GOTO mike
    /WINDOW GOTO #irssi
    /WINDOW NEW HIDDEN
    /WINDOW LOG OFF
    /WINDOW LOG ON ~/logs/debug.log
    /WINDOW LEVEL -ALL +NOTICES
    /WINDOW HIDELEVEL ^JOINS ^PARTS ^QUITS
    /WINDOW LOGFILE ~/logs/notices.log

%9See also:%9 JOIN, LEVELS, LOG, QUERY, SET window_default_level, SET window_default_hidelevel

