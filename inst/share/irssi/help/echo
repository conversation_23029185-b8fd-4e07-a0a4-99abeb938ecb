
%9Syntax:%9

ECHO %|[-window <name>] [-level <level>] <text>

%9Parameters:%9

    -window:     Displays the output in the target window.
    -level:      Displays the output with a given message level.

    The text output; if no target is given, the active window will be used.

%9Description:%9

    Displays the given text.

%9Examples:%9

    /ECHO 1 + 1 = 2 :D
    /ECHO Testing the ECHO command
    /ECHO -window #irssi Special variables such as ${N} will not be expanded.

%9See also:%9 CAT, EVAL, EXEC, LEVELS

