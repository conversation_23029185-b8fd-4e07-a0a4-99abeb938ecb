
%9Syntax:%9

ALIAS %|[[-]<alias> [<command>]]

%9Parameters:%9

    A name of the alias and the command to execute. You can prepend the alias
    with the '-' character to remove the alias; if no argument is given, your
    aliases will be displayed.

%9Description:%9

    Creates or updates an alias; you can use the ';' character to separate
    multiple commands.

    The parameters given to the alias are expanded in '$[\d]'; for example $0,
    $1, $2, $8, ..., as well as any other special variable.

    If you don't use any parameters in your alias, all parameters will be
    automatically appended after it.

%9Examples:%9

    /ALIAS
    /ALIAS UH USERHOST
    /ALIAS COMEBACK SAY I was hoping for a battle of wits, but you seem to be unarmed.
    /ALIAS -COMEBACK
    /ALIAS UNACT SCRIPT EXEC \$_->activity(0) for Irssi::windows
    /ALIAS QOP ^MSG Q op $C

%9References:%9

    https://github.com/irssi/irssi/blob/master/docs/special_vars.txt

%9See also:%9 BIND, UNALIAS

