
%9Syntax:%9

EVAL %|<command(s)>

%9Parameters:%9

    The commands to evaluate.

%9Description:%9

    Evaluates the given commands and executes them; you can use internal
    variables and separate multiple commands by using the `;' character.
    If the command contains a string with `$', `\' or `;' those characters
    need to be escaped:

        `$' -> `$$'
        `\' -> `\\' (or even `\\\\', depending on where they are used)
        `;' -> `\;'

%9Examples:%9

    /EVAL echo I am connected to ${S} on ${chatnet} as ${N}
    /EVAL echo My user privileges are +${usermode}; echo Let's party!

    to print `1;2$3\4':

    /EVAL echo 1\;2$$3\\4

%9References:%9

    https://github.com/irssi/irssi/blob/master/docs/special_vars.txt

%9See also:%9 CAT, CD, ECHO, EXEC

