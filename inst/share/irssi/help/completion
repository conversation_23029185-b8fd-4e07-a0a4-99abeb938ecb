
%9Syntax:%9

COMPLETION %|[-auto] [-delete] <key> <value>

%9Parameters:%9

    -auto:      Inserts the completion without pressing a word completion
                character.
    -delete:    Removes the completion from the configuration.

    A key and the value to use as a replacement. If no argument is given, the
    list of completions will be displayed.

%9Description:%9

    Replaces or completed words or letters; you can write just the first few
    letters of the word and press TAB to insert a replacement.

    When a replacement has been found, <PERSON><PERSON><PERSON> will choose the most probable
    matching word and replaces it; you may press TAB repeatedly to swap between
    matches.

    If you want to remove a completion which has the auto parameter set, you
    need to enclose the completion between "'" characters.

%9Examples:%9

    /COMPLETION w/h without
    /COMPLETION -auto anywya anyway
    /COMPLETION -delete 'anywya'
    /COMPLETION -delete without

%9See also:%9 BIND

