.TH Irssi 1 "June 2015" "Irssi IRC client"
.SH NAME
Irssi \- a modular IRC client for UNIX
.SH SYNOPSIS
.B irssi
[--config=PATH] [--home=PATH] [-dv!?] [-c server] [-p port] [-n nickname]
[-w password] [-h hostname]
.SH DESCRIPTION
.B Irssi
is a modular Internet Relay Chat client; it is highly extensible and
very secure. Being a fullscreen, termcap based client with many
features,
.B Irssi
is easily extensible through scripts and modules.
.SH OPTIONS
.TP
.BI "\-\-config="FILE
use 
.I FILE
instead of ~/.irssi/config
.TP
.BI "\-\-home="PATH
.I PATH 
specifies the home directory of <PERSON><PERSON><PERSON>; default is 
.BR ~/.irssi
.TP
.BI "\-c, \-\-connect="SERVER
connect to 
.I SERVER
.TP
.BI "\-w, \-\-password="PASSWORD
use
.I PASSWORD 
for authentication.
.TP
.BI "\-p, \-\-port="PORT
automatically connect to 
.I PORT 
on server.
.TP
.BI "\-!, \-\-noconnect"
disable autoconnecting of servers
.TP
.BI "\-n, \-\-nick="NICKNAME
specify 
.I NICKNAME 
as your nick
.TP
.BI "\-h, \-\-hostname="HOSTNAME
use
.I HOSTNAME
for your irc session
.TP
.BI "\-v, \-\-version"
display the version of Irssi
.TP
.BI "\-?, \-\-help"
show a help message
.SH SEE ALSO
.B Irssi
has a solid amount of documentation available; check /HELP or look online
at https://irssi.org
.SH FILES
.TP
.I ~/.irssi/config
personal configuration file
.TP
.I ~/.irssi/config.autosave
automatic save of the personal config file when it was changed externally
.TP
.I ~/.irssi/default.theme
default irssi theme
.TP
.I ~/.irssi/away.log
logged messages in away status
.TP
.I ~/.irssi/scripts/
default scripts directory
.TP
.I ~/.irssi/scripts/autorun/
directory containing links to scripts that should be loaded
automatically on startup
.TP
.I ~/.irssi/startup
file containing a list of commands to execute on startup
.SH AUTHORS/CREDITS
.B Irssi
was written by Timo Sirainen; this manpage was written by Istvan Sebestyen, Stefan Tomanek, Geert Hauwaerts.
