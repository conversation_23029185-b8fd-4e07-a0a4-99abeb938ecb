
 Irssi installation instructions
 -------------------------------

To compile Irssi you need:

- meson-0.53 build system with ninja-1.8 or greater
- pkg-config (or compatible)
- glib-2.32 or greater
- openssl (for ssl support)
- perl-5.8 or greater (for building, and optionally Perl scripts)
- terminfo or ncurses (for text frontend)

For most people, this should work just fine:

 meson Build
 ninja -C Build
 su
 ninja -C Build install
 
meson options

  These options can be given to meson.
  
  For a complete list of options, run
   meson configure

  --prefix

  Specifies the path where I<PERSON><PERSON> will be installed.
  YES, you can install Irssi WITHOUT ROOT permissions
  by using --prefix=/home/<USER>

  -Dwith-proxy=yes

  Build the Irssi proxy (see startup-HOWTO).

  -Dwith-perl=[yes|no]

  Enable Perl support
  yes    enable builtin (default)
  no     disable

  -Dwith-perl-lib=[site|vendor|DIR]

  Specify installation dir for Perl libraries
  site   install in dir for site-specific modules (default)
  vendor install in dir for vendor-specific modules
  DIR    install in DIR

  -Dwith-bot=yes

  Build irssi-bot

  -Dwithout-textui=yes

  Build without text frontend

If anything is in non-standard path, you can just give the paths in
the -Dc_args and -Dc_link_args options variable, eg.:

  meson Build -Dc_args='-I/opt/openssl/include' -Dc_link_args='-L/opt/openssl/lib'



 Perl problems
 -------------

Perl support generates most of the problems. There's quite a many
things that can go wrong:

 - If there's any weird crashing at startup, you might have older irssi's
   perl libraries installed somewhere, and you should remove those.
 - If meson complains that it doesn't find some perl stuff, you're
   probably missing libperl.so or libperl.a. In debian, you'll need to do
   apt-get install libperl-dev

You can verify that the perl module is loaded and working with "/LOAD"
command. It should print something like:

Module               Type    Submodules
...
perl                 dynamic fe core


 System specific notes
 ---------------------

 Android

When cross compiling Irssi for Android, you can specify the path of
the cross-perl in the cross file.

You may not have a cross-perl available. In that case, you will have
to manually supply the required Perl arguments in the cross file. See
the commented properties in the example cross file.

An example cross file can be found in the docs folder. To use it, you
would call:

  meson Build --cross-file cross-android-aarch64.txt \
    --prefix /data/data/com.termux/files/usr \
    --libdir lib \
    -Dfhs-prefix=/data/data/com.termux/files/usr \


 Cygwin

Getting perl scripting to work needs a few things:

 - TODO


 Apple MacOS / Darwin

At the time of writing, meson has an open issue with correctly linking
libraries on macos.

See docs/meson-macos-ar.txt for a workaround.
