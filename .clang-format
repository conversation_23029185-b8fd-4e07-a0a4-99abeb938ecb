# IndentPPDirectives: AfterHash
# SpaceInParentheses: false
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignOperands: true
AlignTrailingComments: true
AllowAllParametersOfDeclarationOnNextLine: true
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: Empty
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AlwaysBreakAfterReturnType: None
AlwaysBreakBeforeMultilineStrings: false
BinPackArguments: true
BinPackParameters: true
BreakBeforeBinaryOperators: None
BreakBeforeBraces: Linux
BreakBeforeTernaryOperators: false
ColumnLimit: 100
IndentCaseLabels: false
IndentWidth: 8
IndentWrappedFunctionNames: true
KeepEmptyLinesAtTheStartOfBlocks: false
Language: Cpp
Cpp11BracedListStyle: false
MaxEmptyLinesToKeep: 1
PointerAlignment: Right
SortIncludes: false
SpaceAfterCStyleCast: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesInCStyleCastParentheses: false
SpacesInSquareBrackets: false
TabWidth: 8
UseTab: ForIndentation
