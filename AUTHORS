Original code:

  <PERSON><PERSON> <<EMAIL>>

Irssi staff (current maintainers) <<EMAIL>>:

  <PERSON><PERSON> (senneth, vb)
  <PERSON><PERSON><PERSON> (coekie)
  <PERSON><PERSON> (c0ffee)
  <PERSON><PERSON> (<PERSON>rt)
  <PERSON><PERSON> (jilles)
  <PERSON> (ahf)
  <PERSON> (bazerka)
  dequis (dx)
  <PERSON><PERSON> (Nei)
  <PERSON> (TheLemonMan, lemonboy)

Former developers:

  <PERSON><PERSON> (exg)

Large feature patches by:

  <PERSON> (dg, dgl) : isupport
  <EMAIL> : SSL support
  <PERSON> : SSL certs
  He<PERSON><PERSON> : DCC SEND queueing
  Mark <PERSON> : DCC SERVER
  Francesco <PERSON> : Passive DCC
  Uli Mei<PERSON> : OTR support
  <PERSON> : OTR support

Other patches (grep for "patch" in ChangeLog) by:

  <PERSON>-che <PERSON> (<PERSON>)
  <PERSON> (decadix)
  <PERSON><PERSON> (<PERSON><PERSON><PERSON>)
  <PERSON><PERSON><PERSON>
  <EMAIL>
  <EMAIL>
  <PERSON><PERSON> (Q<PERSON>zak)
  Petr <PERSON> (fuchs)
  <EMAIL>
  BC-bd
  Juerd
  Han
  pv2b
  Tommi Komulainen (tommik)
  <EMAIL>
  <EMAIL>
  <EMAIL>
  <EMAIL>
  Leszek Matok
  <EMAIL>
  <EMAIL>
  <EMAIL>
  <EMAIL>
  Jakub <PERSON>kowski (shasta)
  <EMAIL>
  Tinuk
  Mark Glines
  Kjetil Ødegaard
  Chris Moore
  ComradeP
  Lauri Nurmi
  Mikko Rauhala
  loafier
  Nicolas Collignon
  Daniel Koning
  Yi-Hsuan Hsin
  Jon Mayo
  Johan Kiviniemi
  JasonX
  Lukas Mai (mauke)
  Ismael Luceno
  Thomas Karpiniec
  Svante Kvarnström
  Tom Feist (shabble)
  Sebastian Thorarensen (Sebban)
  Hans Nielsen
  Jari Matilainen (vague)
  Thibault B (isundil)
  kyak
  Vesa Pirila (Lohhari)
  Haw Loeung
  François Revol (mmuman)
  blha303
  Guillaume Brogi (guiniol)
  Adam-
  Robert C Jensen
  Paul Johnson
  KindOne
  Fabian Kurz
  Todd Pratt
  xavierog
