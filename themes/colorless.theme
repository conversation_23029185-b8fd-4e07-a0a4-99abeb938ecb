# When testing changes, the easiest way to reload the theme is with /RELOAD.
# This reloads the configuration file too, so if you did any changes remember
# to /SAVE it first. Remember also that /SAVE overwrites the theme file with
# old data so keep backups :)

# TEMPLATES:

# The real text formats that i<PERSON><PERSON> uses are the ones you can find with
# /FORMAT command. Back in the old days all the colors and texts were mixed
# up in those formats, and it was really hard to change the colors since you
# might have had to change them in tens of different places. So, then came
# this templating system.

# Now the /FORMATs don't have any colors in them, and they also have very
# little other styling. Most of the stuff you need to change is in this
# theme file. If you can't change something here, you can always go back
# to change the /FORMATs directly, they're also saved in these .theme files.

# So .. the templates. They're those {blahblah} parts you see all over the
# /FORMATs and here. Their usage is simply {name parameter1 parameter2}.
# When i<PERSON><PERSON> sees this kind of text, it goes to find "name" from abstracts
# block below and sets "parameter1" into $0 and "parameter2" into $1 (you
# can have more parameters of course). Templates can have subtemplates.
# Here's a small example:
#   /FORMAT format hello {colorify {underline world}}
#   abstracts = { colorify = "%G$0-%n"; underline = "%U$0-%U"; }
# When irssi expands the templates in "format", the final string would be:
#   hello %G%Uworld%U%n
# ie. underlined bright green "world" text.
# and why "$0-", why not "$0"? $0 would only mean the first parameter,
# $0- means all the parameters. With {underline hello world} you'd really
# want to underline both of the words, not just the hello (and world would
# actually be removed entirely).

# COLORS:

# You can find definitions for the color format codes in docs/formats.txt.

# There's one difference here though. %n format. Normally it means the
# default color of the terminal (white mostly), but here it means the
# "reset color back to the one it was in higher template". For example
# if there was /FORMAT test %g{foo}bar, and foo = "%Y$0%n", irssi would
# print yellow "foo" (as set with %Y) but "bar" would be green, which was
# set at the beginning before the {foo} template. If there wasn't the %g
# at start, the normal behaviour of %n would occur. If you _really_ want
# to use the terminal's default color, use %N.

#############################################################################

# default foreground color (%N) - -1 is the "default terminal color"
default_color = "-1";

# print timestamp/servertag at the end of line, not at beginning
info_eol = "false";

# these characters are automatically replaced with specified color
# (dark grey by default)
#replaces = { "[]=" = "%K$*%n"; };

abstracts = {
  ##
  ## generic
  ##

  # text to insert at the beginning of each non-message line
  line_start = "-!- ";

  # timestamp styling, nothing by default
  timestamp = "$*";

  # any kind of text that needs hilighting, default is to bold
  hilight = "%_$*%_";

  # any kind of error message, default is bright red
  error = "%_$*%_";

  # channel name is printed
  channel = "$*";

  # nick is printed
  nick = "$*";

  # nick host is printed
  nickhost = "[$*]";

  # server name is printed
  server = "$*";

  # some kind of comment is printed
  comment = "[$*]";

  # reason for something is printed (part, quit, kick, ..)
  reason = "{comment $*}";

  # mode change is printed ([+o nick])
  mode = "{comment $*}";

  ##
  ## channel specific messages
  ##

  # highlighted nick/host is printed (joins)
  channick_hilight = "$*";
  chanhost_hilight = "{nickhost $*}";

  # nick/host is printed (parts, quits, etc.)
  channick = "$*";
  chanhost = "{nickhost $*}";

  # highlighted channel name is printed
  channelhilight = "$*";

  # ban/ban exception/invite list mask is printed
  ban = "$*";

  ##
  ## messages
  ##

  # the basic styling of how to print message, $0 = nick mode, $1 = nick
  msgnick = "<$0$1-> %|";

  # message from you is printed. "msgownnick" specifies the styling of the
  # nick ($0 part in msgnick) and "ownmsgnick" specifies the styling of the
  # whole line.

  # Example1: You want the message text to be green:
  #  ownmsgnick = "{msgnick $0 $1-}%g";
  # Example2.1: You want < and > chars to be yellow:
  #  ownmsgnick = "%Y{msgnick $0 $1-%Y}%n";
  #  (you'll also have to remove <> from replaces list above)
  # Example2.2: But you still want to keep <> grey for other messages:
  #  pubmsgnick = "%K{msgnick $0 $1-%K}%n";
  #  pubmsgmenick = "%K{msgnick $0 $1-%K}%n";
  #  pubmsghinick = "%K{msgnick $1 $0$2-%n%K}%n";
  #  ownprivmsgnick = "%K{msgnick  $*%K}%n";
  #  privmsgnick = "%K{msgnick  %R$*%K}%n";

  # $0 = nick mode, $1 = nick
  ownmsgnick = "{msgnick $0 $1-}";
  ownnick = "$*";

  # public message in channel, $0 = nick mode, $1 = nick
  pubmsgnick = "{msgnick $0 $1-}";
  pubnick = "$*";

  # public message in channel meant for me, $0 = nick mode, $1 = nick
  pubmsgmenick = "{msgnick $0 $1-}";
  menick = "%_$*%_";

  # public highlighted message in channel
  # $0 = highlight color, $1 = nick mode, $2 = nick
  pubmsghinick = "{msgnick $1 $0$2-%n}";

  # channel name is printed with message
  msgchannel = ":$*";

  # private message, $0 = nick, $1 = host
  privmsg = "[$0($1-)] ";

  # private message from you, $0 = "msg", $1 = target nick
  ownprivmsg = "[$0($1-)] ";

  # own private message in query
  ownprivmsgnick = "{msgnick  $*}";
  ownprivnick = "$*";

  # private message in query
  privmsgnick = "{msgnick  $*}";

  ##
  ## Actions (/ME stuff)
  ##

  # used internally by this theme
  action_core = " * $*";

  # generic one that's used by most actions
  action = "{action_core $*} ";

  # own action, both private/public
  ownaction = "{action $*}";

  # own action with target, both private/public
  ownaction_target = "{action_core $0}:$1 ";

  # private action sent by others
  pvtaction = " (*) $* ";
  pvtaction_query = "{action $*}";

  # public action sent by others
  pubaction = "{action $*}";


  ##
  ## other IRC events
  ##

  # whois
  whois = "%# $[8]0 : $1-";

  # notices
  ownnotice = "[$0($1-)] ";
  notice = "-$*- ";
  pubnotice_channel = ":$*";
  pvtnotice_host = "($*)";
  servernotice = "!$* ";

  # CTCPs
  ownctcp = "[$0($1-)] ";
  ctcp = "$*";

  # wallops
  wallop = "$*: ";
  wallop_nick = "$*";
  wallop_action = " * $* ";

  # netsplits
  netsplit = "$*";
  netjoin = "$*";

  # /names list
  names_prefix = "";
  names_nick = "[$0$1-] ";
  names_nick_op = "{names_nick $*}";
  names_nick_halfop = "{names_nick $*}";
  names_nick_voice = "{names_nick $*}";
  names_users = "[$*]";
  names_channel = "$*";

  # DCC
  dcc = "$*";
  dccfile = "$*";

  # DCC chat, own msg/action
  dccownmsg = "[$0($1-)] ";
  dccownnick = "$*";
  dccownquerynick = "$*";
  dccownaction = "{action $*}";
  dccownaction_target = "{action_core $0}:$1 ";

  # DCC chat, others
  dccmsg = "[$1-($0)] ";
  dccquerynick = "$*";
  dccaction = " (*dcc*) $* %|";

  ##
  ## statusbar
  ##

  # default background for all statusbars. You can also give
  # the default foreground color for statusbar items.
  sb_background = "%8";
  window_border = "%8";

  # default backround for "default" statusbar group
  #sb_default_bg = "%8";
  # background for prompt / input line
  sb_prompt_bg = "%n";
  # background for info statusbar
  sb_info_bg = "%8";
  # background for topicbar (same default)
  #sb_topic_bg = "%8";

  # text at the beginning of statusbars. "sb" already puts a space there,
  # so we don't use anything by default.
  sbstart = "";
  # text at the end of statusbars. Use space so that it's never
  # used for anything.
  sbend = " ";

  prompt = "[$*] ";

  sb = " [$*]";
  sbmode = "(+$*)";
  sbaway = " (zZzZ)";
  sbservertag = ":$0 (change with ^X)";

  # activity in statusbar

  # ',' separator
  sb_act_sep = "$*";
  # normal text
  sb_act_text = "$*";
  # public message
  sb_act_msg = "%_$*%_";
  # hilight
  sb_act_hilight = "%8$*%8";
  # hilight with specified color, $0 = color, $1 = text
  sb_act_hilight_color = "$0$1-%n";
};
