option('without-textui',    type : 'combo',  description : 'Build without text frontend',             	  choices : ['no', 'yes'])
option('with-bot',          type : 'combo',  description : 'Build irssi-bot',                         	  choices : ['no', 'yes'])
option('with-fuzzer',       type : 'combo',  description : 'Build irssi-fuzzer',                      	  choices : ['no', 'yes'])
option('with-fuzzer-lib',   type : 'string', description : 'Specify path to fuzzer library', value : '-fsanitize=fuzzer')
option('fuzzer-link-language', type : 'string', description : 'The linker to use for the fuzz targets [c, cpp]', value : 'c')
option('with-proxy',        type : 'combo',  description : 'Build irssi-proxy',                       	  choices : ['no', 'yes'])
option('with-perl-lib',     type : 'string', description : 'Specify where to install the Perl libraries for Irssi')
option('with-perl',         type : 'combo',  description : 'Build with Perl support',                     choices : ['auto', 'yes', 'no'])
option('with-otr',          type : 'combo',  description : 'Build with OTR support',                      choices : ['auto', 'yes', 'no'])
option('disable-utf8proc',  type : 'combo',  description : 'Build without Julia\'s utf8proc',             choices : ['auto', 'yes', 'no'])
option('with-capsicum',     type : 'combo',  description : 'Build with Capsicum support',                 choices : ['auto', 'yes', 'no'])
option('static-dependency', type : 'combo',  description : 'Request static dependencies',                 choices : ['no', 'yes'])
option('install-glib',      type : 'combo',  description : 'Download and install GLib for you',           choices : ['no', 'yes', 'force'])
option('docdir',            type : 'string', description : 'Documentation directory')
option('fhs-prefix',        type : 'string', description : 'System prefix for Termux')
option('PACKAGE_VERSION',   type : 'string', description : 'Override PACKAGE_VERSION in tarballs')
