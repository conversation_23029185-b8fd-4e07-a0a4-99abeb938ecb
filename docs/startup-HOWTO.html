<base href='https://irssi.org/documentation/startup/'>
<h1>Startup How-To</h1>
    <h3 id="to-new-irssi-users-not-to-new-irc-users-">To new Irssi users (not to new IRC users ..)</h3>

<p>Copyright (c) 2000-2002 by <PERSON><PERSON>, release under <a href="https://www.gnu.org/licenses/fdl.html">GNU FDL</a> 1.1 license.</p>

<p>Index with some FAQ questions that are answered in the chapter:</p>

<ol>
  <li><a href="#first-steps">First steps</a></li>
  <li><a href="#basic-user-interface-usage">Basic user interface usage</a>
    <ul>
      <li>Split windows work in weird way</li>
      <li>How can I easily switch between windows?</li>
      <li>But alt-1 etc. don’t work!</li>
    </ul>
  </li>
  <li><a href="#server-and-channel-automation">Server and channel automation</a>
    <ul>
      <li>How do I automatically connect to servers at startup?</li>
      <li>How do I automatically join to channels at startup?</li>
      <li>How do I automatically send commands to server at connect?</li>
    </ul>
  </li>
  <li><a href="#setting-up-windows-and-automatically-restoring-them-at-startup">Setting up windows and automatically restoring them at startup</a></li>
  <li><a href="#status-and-msgs-windows--message-levels">Status and msgs windows &amp; message levels</a>
    <ul>
      <li>I want /WHOIS to print reply to current window</li>
      <li>I want all messages to go to one window, not create new windows</li>
    </ul>
  </li>
  <li><a href="#how-support-for-multiple-servers-works-in-irssi">How support for multiple servers works in irssi</a>
    <ul>
      <li>I connected to some server that doesn’t respond and now irssi keeps trying to reconnect to it again and again, how can I stop it??</li>
      <li>I want to have own status and/or msgs window for each servers</li>
    </ul>
  </li>
  <li><a href="#lastlog-and-jumping-around-in-scrollback">/LASTLOG and jumping around in scrollback</a>
    <ul>
      <li>How can I save all texts in a window to file?</li>
    </ul>
  </li>
  <li><a href="#logging">Logging</a></li>
  <li><a href="#changing-keyboard-bindings">Changing keyboard bindings</a>
    <ul>
      <li>How do I make F1 key do something?</li>
    </ul>
  </li>
  <li><a href="#proxies-and-irc-bouncers">Proxies and IRC bouncers</a></li>
  <li><a href="#irssis-settings">Irssi’s settings</a>
    <ul>
      <li><a href="#for-all-the-ircii-people">For all the ircII people</a></li>
    </ul>
  </li>
  <li><a href="#statusbar">Statusbar</a>
    <ul>
      <li>I loaded a statusbar script but it’s not visible anywhere!</li>
    </ul>
  </li>
</ol>

<h2 id="first-steps">1. First steps</h2>

<p>IRC Networks are made of servers, and servers have channels. The default config has a few predefined networks, to list them:</p>

<div><div><pre><code>/NETWORK LIST
</code></pre></div></div>

<p>And to connect to one of those networks and join a channel:</p>

<div><div><pre><code>/CONNECT liberachat
/JOIN #irssi
</code></pre></div></div>

<p>To add more networks:</p>

<div><div><pre><code>/NETWORK ADD ExampleNet
</code></pre></div></div>

<p>Then add some servers (with -auto to automatically connect):</p>

<div><div><pre><code>/SERVER ADD -auto -network ExampleNet irc.example.net
</code></pre></div></div>

<p>Automatically join to channels after connected to server:</p>

<div><div><pre><code>/CHANNEL ADD -auto #lounge ExampleNet
</code></pre></div></div>

<p>To modify existing networks (or servers, or channels) just ADD again using the same name as before. This configures a network to identify with nickserv and wait for 2 seconds before joining channels:</p>

<div><div><pre><code>/NETWORK ADD -autosendcmd "/^msg nickserv ident pass;wait 2000" ExampleNet
</code></pre></div></div>

<p>If you have irssi 0.8.18 or higher and the irc network supports it, you can use SASL instead of nickserv, which is more reliable:</p>

<div><div><pre><code>/NETWORK ADD -sasl_username yourname -sasl_password yourpassword -sasl_mechanism PLAIN liberachat
</code></pre></div></div>

<p>These commands have many more options, see their help for details:</p>

<div><div><pre><code>/HELP NETWORK
/HELP SERVER
/HELP CHANNEL
/HELP
</code></pre></div></div>

<p>If you want lines containing your nick to hilight:</p>

<div><div><pre><code>/HILIGHT nick
</code></pre></div></div>

<p>Or, for irssi 0.8.18 or higher:</p>

<div><div><pre><code>/SET hilight_nick_matches_everywhere ON
</code></pre></div></div>

<p>To get beeps on private messages or highlights:</p>

<div><div><pre><code>/SET beep_msg_level MSGS HILIGHT DCCMSGS
</code></pre></div></div>

<p>No other irssi settings are needed (don’t enable bell_beeps), but there may be settings to change in your terminal multiplexer (screen/tmux), your terminal, or your desktop environment.</p>

<h2 id="basic-user-interface-usage">2. Basic user interface usage</h2>

<p>Windows can be scrolled up/down with PgUp and PgDown keys. If they don’t work for you, use Meta-p and Meta-n keys. For jumping to beginning or end of the buffer, use <code>/SB HOME</code> and <code>/SB END</code> commands.</p>

<p>By default, irssi uses “hidden windows” for everything. Hidden windows are created every time you <code>/JOIN</code> a channel or <code>/QUERY</code> someone. There’s several ways you can change between these windows:</p>

<div><div><pre><code>Meta-1, Meta-2, .. Meta-0 - Jump directly between windows 1-10
Meta-q .. Meta-o          - Jump directly between windows 11-19
/WINDOW &lt;number&gt;          - Jump to any window with specified number
Ctrl-P, Ctrl-N            - Jump to previous / next window
</code></pre></div></div>

<p>Clearly the easiest way is to use Meta-number keys. Meta usually means the ALT key, but if that doesn’t work, you can use ESC.</p>

<p>Mac OS X users with ALT key issues might prefer using <a href="https://www.iterm2.com/">iTerm2</a> instead of the default terminal emulator.</p>

<h3 id="alt-key-as-meta-for-xtermrxvt-users">Alt key as meta, for xterm/rxvt users</h3>

<p>If you use xterm or rxvt, you may need to set a few X resources:</p>

<div><div><pre><code> XTerm*eightBitInput:   false
 XTerm*metaSendsEscape: true
</code></pre></div></div>

<p>With rxvt, you can also specify which key acts as Meta key. So if you want to use ALT instead of Windows key for it, use:</p>

<div><div><pre><code> rxvt*modifier: alt
</code></pre></div></div>

<p>You could do this by changing the X key mappings:</p>

<div><div><pre><code> xmodmap -e "keysym Alt_L = Meta_L Alt_L"
</code></pre></div></div>

<p>And how exactly do you set these X resources? For Debian, there’s <code>/etc/X11/Xresources/xterm</code> file where you can put them and it’s read automatically when X starts. <code>~/.Xresources</code> and <code>~/.Xdefaults</code> files might also work. If you can’t get anything else to work, just copy and paste those lines to <code>~/.Xresources</code> and directly call <code>xrdb -merge ~/.Xresources</code> in some xterm. The resources affect only the new xterms you start, not existing ones.</p>

<h3 id="split-windows-and-window-items">Split windows and window items</h3>

<p><em>Note: <a href="https://quadpoint.org/articles/irssisplit/">this guide</a> might be a better introduction to window splits</em></p>

<p>Irssi also supports split windows, they’ve had some problems in past but I think they should work pretty well now :) Here’s some commands related to them:</p>

<div><div><pre><code>/WINDOW NEW                    - Create new split window
/WINDOW NEW HIDE               - Create new hidden window
/WINDOW CLOSE                  - Close split or hidden window

/WINDOW HIDE [&lt;number&gt;|&lt;name&gt;] - Make the split window hidden window
/WINDOW SHOW &lt;number&gt;|&lt;name&gt;   - Make the hidden window a split window

/WINDOW SHRINK [&lt;lines&gt;]       - Shrink the split window
/WINDOW GROW [&lt;lines&gt;]         - Grow the split window
/WINDOW BALANCE                - Balance the sizes of all split windows
</code></pre></div></div>

<p>By default, irssi uses “sticky windowing” for split windows. This means that windows created inside one split window cannot be moved to another split window without some effort. For example you could have following window layout:</p>

<div><div><pre><code> Split window 1: win#1 - Status window, win#2 - Messages window
 Split window 2: win#3 - IRCnet/#channel1, win#4 - IRCnet/#channel2
 Split window 3: win#5 - efnet/#channel1, win#6 - efnet/#channel2
</code></pre></div></div>

<p>When you are in win#1 and press ALT-6, irssi jumps to split window #3 and moves the efnet/#channel2 the active window.</p>

<p>With non-sticky windowing the windows don’t have any relationship with split windows, pressing ALT-6 in win#1 moves win#6 to split window 1 and sets it active, except if win#6 was already visible in some other split window irssi just changes to that split window. This it the way windows work with ircii, if you prefer it you can set it with</p>

<div><div><pre><code>/SET autostick_split_windows OFF
</code></pre></div></div>

<p>Each window can have multiple channels, queries and other “window items” inside them. If you don’t like windows at all, you disable automatic creating of them with</p>

<div><div><pre><code>/SET autocreate_windows OFF
</code></pre></div></div>

<p>And if you keep all channels in one window, you most probably want the channel name printed in each line:</p>

<div><div><pre><code>/SET print_active_channel ON
</code></pre></div></div>

<p>If you want to group only some channels or queries in one window, use</p>

<div><div><pre><code>/JOIN -window #channel
/QUERY -window nick
</code></pre></div></div>

<h2 id="server-and-channel-automation">3. Server and channel automation</h2>

<p>Irssi’s multiple IRC network support is IMHO very good - at least compared to other clients :) Even if you’re only in one IRC network you should group all your servers to be in the same IRC network as this helps with reconnecting if your primary server breaks and is probably useful in some other ways too :) For information how to actually use irssi correctly with multiple servers see the chapter 6.</p>

<p>First you need to have your IRC network set, use <code>/NETWORK</code> command to see if it’s already there. If it isn’t, use <code>/NETWORK ADD yournetwork</code>. If you want to execute some commands automatically when you’re connected to some network, use <code>-autosendcmd</code> option. Here’s some examples:</p>

<div><div><pre><code>/NETWORK ADD -autosendcmd '^msg bot invite' IRCnet
/NETWORK ADD -autosendcmd "/^msg nickserv ident pass;wait 2000" OFTC
</code></pre></div></div>

<p>After that you need to add your servers. For example:</p>

<div><div><pre><code>/SERVER ADD -auto -network IRCnet irc.kpnqwest.fi 6667
/SERVER ADD -auto -network worknet irc.mycompany.com 6667 password
</code></pre></div></div>

<p>The <code>-auto</code> option specifies that this server is automatically connected at startup. You don’t need to make more than one server with <code>-auto</code> option to one IRC network, other servers are automatically connected in same network if the <code>-auto</code> server fails.</p>

<p>And finally channels:</p>

<div><div><pre><code>/CHANNEL ADD -auto -bots *!*<EMAIL> -botcmd "/^msg $0 op pass" #irssi efnet
/CHANNEL ADD -auto #secret IRCnet password
</code></pre></div></div>

<p><code>-bots</code> and <code>-botcmd</code> should be the only ones needing a bit of explaining. They’re used to send commands automatically to bot when channel is joined, usually to get ops automatically. You can specify multiple bot masks with <code>-bots</code> option separated with spaces (and remember to quote the string then). The $0 in <code>-botcmd</code> specifies the first found bot in the list. If you don’t need the bot masks (ie. the bot is always with the same nick, like chanserv) you can give only the <code>-botcmd</code> option and the command is always sent.</p>

<h2 id="setting-up-windows-and-automatically-restoring-them-at-startup">4. Setting up windows and automatically restoring them at startup</h2>

<p>First connect to all the servers, join the channels and create the queries you want. If you want to move the windows or channels around use commands:</p>

<div><div><pre><code>/WINDOW MOVE LEFT/RIGHT/number    - move window elsewhere
/WINDOW ITEM MOVE &lt;number&gt;|&lt;name&gt; - move channel/query to another window
</code></pre></div></div>

<p>When everything looks the way you like, use <code>/LAYOUT SAVE</code> command (and <code>/SAVE</code>, if you don’t have autosaving enabled) and when you start irssi next time, irssi remembers the positions of the channels, queries and everything. This “remembering” doesn’t mean that simply using <code>/LAYOUT SAVE</code> would automatically make irssi reconnect to all servers and join all channels, you’ll need the <code>/SERVER ADD -auto</code> and <code>/CHANNEL ADD -auto</code> commands to do that.</p>

<p>If you want to change the layout, you just rearrange the layout like you want it and use <code>/LAYOUT SAVE</code> again. If you want to remove the layout for some reason, use <code>/LAYOUT RESET.</code></p>

<h2 id="status-and-msgs-windows--message-levels">5. Status and msgs windows &amp; message levels</h2>

<p>By default, all the “extra messages” go to status window. This means pretty much all messages that don’t clearly belong to some channel or query. Some people like it, some don’t. If you want to remove it, use</p>

<div><div><pre><code>/SET use_status_window OFF
</code></pre></div></div>

<p>This doesn’t have any effect until you restart irssi. If you want to remove it immediately, just <code>/WINDOW CLOSE</code> it.</p>

<p>Another common window is “messages window”, where all private messages go. By default it’s disabled and query windows are created instead. To make all private messages go to msgs window, say:</p>

<div><div><pre><code>/SET use_msgs_window ON
/SET autocreate_query_level DCCMSGS  (or if you don't want queries to
 				      dcc chats either, say NONE)
</code></pre></div></div>

<p>use_msgs_window either doesn’t have any effect until restarting irssi. To create it immediately say:</p>

<div><div><pre><code>/WINDOW NEW HIDE     - create the window
/WINDOW NAME (msgs)  - name it to "(msgs)"
/WINDOW LEVEL MSGS   - make all private messages go to this window
/WINDOW MOVE 1       - move it to first window
</code></pre></div></div>

<p>Note that neither use_msgs_window nor use_status_window have any effect at all if <code>/LAYOUT SAVE</code> has been used.</p>

<p>This brings us to message levels.. What are they? All messages that irssi prints have one or more “message levels”. Most common are PUBLIC for public messages in channels, MSGS for private messages and CRAP for all sorts of messages with no real classification. You can get a whole list of levels with</p>

<div><div><pre><code>/HELP levels
</code></pre></div></div>

<p>Status window has message level <code>ALL -MSGS</code>, meaning that all messages, except private messages, without more specific place go to status window. The <code>-MSGS</code> is there so it doesn’t conflict with messages window.</p>

<h2 id="how-support-for-multiple-servers-works-in-irssi">6. How support for multiple servers works in irssi</h2>

<p>ircii and several other clients support multiple servers by placing the connection into some window. IRSSI DOES NOT. There is no required relationship between window and server. You can connect to 10 servers and manage them all in just one window, or join channel in each one of them to one single window if you really want to. That being said, here’s how you do connect to new server without closing the old connection:</p>

<div><div><pre><code>/CONNECT irc.server.org
</code></pre></div></div>

<p>Instead of the <code>/SERVER</code> which disconnects the existing connection. To see list of all active connections, use <code>/SERVER</code> without any parameters. You should see a list of something like:</p>

<div><div><pre><code> -!- IRCNet: irc.song.fi:6667 (IRCNet)
 -!- OFTC: irc.oftc.net:6667 (OFTC)
 -!- RECON-1: ***********:6667 () (02:59 left before reconnecting)
</code></pre></div></div>

<p>Here you see that we’re connected to IRCNet and OFTC networks. The IRCNet at the beginning is called the “server tag” while the (IRCnet) at the end shows the IRC network. Server tag specifies unique tag to refer to the server, usually it’s the same as the IRC network. When the IRC network isn’t known it’s some part of the server name. When there’s multiple connections to same IRC network or server, irssi adds a number after the tag so there could be network, network2, network3 etc.</p>

<p>Server tags beginning with <code>RECON-</code> mean server reconnections. Above we see that connection to server at *********** wasn’t successful and irssi will try to connect it again in 3 minutes.</p>

<p>To disconnect one of the servers, or to stop irssi from reconnecting, use</p>

<div><div><pre><code>/DISCONNECT network   - disconnect server with tag "network"
/DISCONNECT recon-1  - stop trying to reconnect to RECON-1 server
/RMRECONNS           - stop all server reconnections

/RECONNECT recon-1   - immediately try reconnecting back to RECON-1
/RECONNECT ALL       - immediately try reconnecting back to all
 		       servers in reconnection queue
</code></pre></div></div>

<p>Now that you’re connected to all your servers, you’ll have to know how to specify which one of them you want to use. One way is to have an empty window, like status or msgs window. In it, you can specify which server to set active with</p>

<div><div><pre><code>/WINDOW SERVER tag    - set server "tag" active
Ctrl-X                - set the next server in list active
</code></pre></div></div>

<p>When the server is active, you can use it normally. When there’s multiple connected servers, irssi adds [servertag] prefix to all messages in non-channel/query messages so you’ll know where it came from.</p>

<p>Several commands also accept <code>-servertag</code> option to specify which server it should use:</p>

<div><div><pre><code>/MSG -tag nick message
/JOIN -tag #channel
/QUERY -tag nick
</code></pre></div></div>

<p><code>/MSG</code> tab completion also automatically adds the <code>-tag</code> option when nick isn’t in active server.</p>

<p>Window’s server can be made sticky. When sticky, it will never automatically change to anything else, and if server gets disconnected, the window won’t have any active server. When the server gets connected again, it is automatically set active in the window. To set the window’s server sticky use</p>

<div><div><pre><code>/WINDOW SERVER -sticky tag
</code></pre></div></div>

<p>This is useful if you wish to have multiple status or msgs windows, one for each server. Here’s how to do them (repeat for each server)</p>

<div><div><pre><code>/WINDOW NEW HIDE
/WINDOW NAME (status)
/WINDOW LEVEL ALL -MSGS
/WINDOW SERVER -sticky network

/WINDOW NEW HIDE
/WINDOW NAME (msgs)
/WINDOW LEVEL MSGS
/WINDOW SERVER -sticky network
</code></pre></div></div>

<h2 id="lastlog-and-jumping-around-in-scrollback">7. /LASTLOG and jumping around in scrollback</h2>

<p><code>/LASTLOG</code> command can be used for searching texts in scrollback buffer. Simplest usages are</p>

<div><div><pre><code>/LASTLOG word     - print all lines with "word" in them
/LASTLOG word 10  - print last 10 occurances of "word"
/LASTLOG -topics  - print all topic changes
</code></pre></div></div>

<p>If there’s more than 1000 lines to be printed, irssi thinks that you probably made some mistake and won’t print them without <code>-force</code> option. If you want to save the full lastlog to file, use</p>

<div><div><pre><code>/LASTLOG -file ~/irc.log
</code></pre></div></div>

<p>With <code>-file</code> option you don’t need <code>-force</code> even if there’s more than 1000 lines. <code>/LASTLOG</code> has a lot of other options too, see <code>/HELP lastlog</code> for details.</p>

<p>Once you’ve found the lines you were interested in, you might want to check the discussion around them. Irssi has <code>/SCROLLBACK</code> (or alias <code>/SB</code>) command for jumping around in scrollback buffer. Since <code>/LASTLOG</code> prints the timestamp when the message was originally printed, you can use <code>/SB GOTO hh:mm</code> to jump directly there. To get back to the bottom of scrollback, use <code>/SB END</code> command.</p>

<h2 id="logging">8. Logging</h2>

<p>Irssi can automatically log important messages when you’re set away (<code>/AWAY reason</code>). When you set yourself unaway (<code>/AWAY</code>), the new messages in away log are printed to screen. You can configure it with:</p>

<div><div><pre><code>/SET awaylog_level MSGS HILIGHT     - Specifies what messages to log
/SET awaylog_file ~/.irssi/away.log - Specifies the file to use
</code></pre></div></div>

<p>Easiest way to start logging with Irssi is to use autologging. With it Irssi logs all channels and private messages to specified directory. You can turn it on with</p>

<div><div><pre><code>/SET autolog ON
</code></pre></div></div>

<p>By default it logs pretty much everything execept CTCPS or CRAP (<code>/WHOIS</code> requests, etc). You can specify the logging level yourself with</p>

<div><div><pre><code>/SET autolog_level ALL -CRAP -CLIENTCRAP -CTCPS (this is the default)
</code></pre></div></div>

<p>By default irssi logs to ~/irclogs/&lt;servertag&gt;/&lt;target&gt;.log. You can change this with</p>

<div><div><pre><code>/SET autolog_path ~/irclogs/$tag/$0.log (this is the default)
</code></pre></div></div>

<p>The path is automatically created if it doesn’t exist. $0 specifies the target (channel/nick). You can make irssi automatically rotate the logs by adding date/time formats to the file name. The formats are in “man strftime” format. For example</p>

<div><div><pre><code>/SET autolog_path ~/irclogs/%Y/$tag/$0.%m-%d.log
</code></pre></div></div>

<p>For logging only some specific channels or nicks, see <code>/HELP log</code></p>

<h2 id="changing-keyboard-bindings">9. Changing keyboard bindings</h2>

<p>You can change any keyboard binding that terminal lets irssi know about. It doesn’t let irssi know everything, so for example shift-backspace can’t be bound unless you modify xterm resources somehow.</p>

<p><code>/HELP bind</code> tells pretty much everything there is to know about keyboard bindings. However, there’s the problem of how to bind some non-standard keys. They might differ a bit with each terminal, so you’ll need to find out what exactly the keypress produces. Easiest way to check that would be to see what it prints in <code>cat</code>. Here’s an example for pressing F1 key:</p>

<div><div><pre><code> [cras@hurina] ~% cat
 ^[OP
</code></pre></div></div>

<p>So in irssi you would use <code>/BIND ^[OP /ECHO F1 pressed</code>. If you use multiple terminals which have different bindings for the key, it would be better to use eg.:</p>

<div><div><pre><code>/BIND ^[OP key F1
/BIND ^[11~ key F1
/BIND F1 /ECHO F1 pressed.
</code></pre></div></div>

<h2 id="proxies-and-irc-bouncers">10. Proxies and IRC bouncers</h2>

<p>Irssi supports connecting to IRC servers via a proxy. All server connections are then made through it, and if you’ve set up everything properly, you don’t need to do any <code>/QUOTE SERVER</code> commands manually.</p>

<p>Here’s an example: You have your bouncer (lets say, BNC or BNC-like) listening in irc.bouncer.org port 5000. You want to use it to connect to servers irc.dal.net and irc.efnet.org. First you’d need to setup the bouncer:</p>

<div><div><pre><code>/SET use_proxy ON
/SET proxy_address irc.bouncer.org
/SET proxy_port 5000

/SET proxy_password YOUR_BNC_PASSWORD_HERE
/SET -clear proxy_string
/SET proxy_string_after conn %s %d
</code></pre></div></div>

<p>Then you’ll need to add the server connections. These are done exactly as if you’d want to connect directly to them. Nothing special about them:</p>

<div><div><pre><code>/SERVER ADD -auto -network dalnet irc.dal.net
/SERVER ADD -auto -network efnet irc.efnet.org
</code></pre></div></div>

<p>With the proxy <code>/SET</code>s however, irssi now connects to those servers through your BNC. All server connections are made through them so you can just forget that your bouncer even exists.</p>

<p>If you don’t want to use the proxy for some reason, there’s <code>-noproxy</code> option which you can give to <code>/SERVER</code> and <code>/SERVER ADD</code> commands.</p>

<p><strong>Proxy specific settings:</strong></p>

<p>All proxies except irssi proxy and socks proxy have these settings in common:</p>

<div><div><pre><code>/SET use_proxy ON
/SET proxy_address &lt;Proxy host address&gt;
/SET proxy_port &lt;Proxy port&gt;
</code></pre></div></div>

<p><strong>HTTP proxy</strong></p>

<p>Use these settings with HTTP proxies:</p>

<div><div><pre><code>/SET -clear proxy_password
/EVAL SET proxy_string CONNECT %s:%d HTTP/1.0\n\n
</code></pre></div></div>

<p><strong>BNC</strong></p>

<div><div><pre><code>/SET proxy_password your_pass
/SET -clear proxy_string
/SET proxy_string_after conn %s %d
</code></pre></div></div>

<p><strong>dircproxy</strong></p>

<p>dircproxy separates the server connections by passwords. So, if you for example have network connection with password ircpass and OFTC connection with oftcpass, you would do something like this:</p>

<div><div><pre><code>/SET -clear proxy_password
/SET -clear proxy_string

/SERVER ADD -auto -network IRCnet fake.network 6667 ircpass
/SERVER ADD -auto -network OFTC fake.oftc 6667 oftcpass
</code></pre></div></div>

<p>The server name and port you give isn’t used anywhere, so you can put anything you want in there.</p>

<p><strong>psyBNC</strong></p>

<p>psyBNC has internal support for multiple servers. However, it could be a bit annoying to use, and some people just use different users for connecting to different servers. You can manage this in a bit same way as with dircproxy, by creating fake connections:</p>

<div><div><pre><code>/SET -clear proxy_password
/SET -clear proxy_string

/NETWORK ADD -user networkuser IRCnet
/SERVER ADD -auto -network IRCnet fake.network 6667 ircpass
/NETWORK ADD -user oftcuser OFTC
/SERVER ADD -auto -network OFTC fake.oftc 6667 oftcpass
</code></pre></div></div>

<p>So, you’ll specify the usernames with <code>/NETWORK ADD</code> command, and the user’s password with <code>/SERVER ADD</code>.</p>

<p><strong>Irssi proxy</strong></p>

<p>Irssi contains it’s own proxy which you can build giving <code>\--with-proxy</code> option to configure. You’ll still need to run irssi in a screen to use it though.</p>

<p>Irssi proxy is a bit different than most proxies, normally proxies create a new connection to IRC server when a new client connects to it, but <strong>irssi proxy shares your existing IRC connection(s) to multiple clients</strong>. And even more clearly: <strong>You can use only one IRC server connection of the irssi proxy to IRC with as many clients as you want</strong>. Can anyone figure out even more easier ways to say this, so I wouldn’t need to try to explain this thing for minutes every time? :)</p>

<p>Irssi proxy supports sharing multiple server connections in different ports, like you can share network in port 2777 and efnet in port 2778.</p>

<p>Usage in proxy side:</p>

<div><div><pre><code>/LOAD proxy
/SET irssiproxy_password &lt;password&gt;
/SET irssiproxy_ports &lt;network&gt;=&lt;port&gt; ... (eg. IRCnet=2777 efnet=2778)
</code></pre></div></div>

<p><strong>NOTE</strong>: you <strong>MUST</strong> add all the servers you are using to server and network lists with <code>/SERVER ADD</code> and <code>/NETWORK ADD</code>. ..Except if you really don’t want to for some reason, and you only use one server connection, you may simply set:</p>

<div><div><pre><code>/SET irssiproxy_ports *=2777
</code></pre></div></div>

<p>Usage in client side:</p>

<p>Just connect to the irssi proxy like it is a normal server with password specified in <code>/SET irssiproxy_password</code>. For example:</p>

<div><div><pre><code>/SERVER ADD -network IRCnet my.irssi-proxy.org 2777 secret
/SERVER ADD -network efnet my.irssi-proxy.org 2778 secret
</code></pre></div></div>

<p>Irssi proxy works fine with other IRC clients as well.</p>

<p><strong>SOCKS</strong></p>

<p>Using <a href="https://github.com/rofl0r/proxychains-ng">proxychains-ng</a> is recommended for using irssi with a socks proxy.</p>

<p>Irssi does not support socks proxy natively.</p>

<p>Note that <code>/SET proxy</code> settings don’t have anything to do with socks.</p>

<p><strong>Others</strong></p>

<p>IRC bouncers usually work like IRC servers, and want a password. You can give it with:</p>

<div><div><pre><code>/SET proxy_password &lt;password&gt;
</code></pre></div></div>

<p>Irssi’s defaults for connect strings are</p>

<div><div><pre><code>/SET proxy_string CONNECT %s %d
/SET proxy_string_after
</code></pre></div></div>

<p>The proxy_string is sent before NICK/USER commands, the proxy_string_after is sent after them. %s and %d can be used with both of them.</p>

<h2 id="irssis-settings">11. Irssi’s settings</h2>

<p>Here’s some settings you might want to change (the default value is shown): Also check the <a href="/documentation/settings/">Settings Documentation</a></p>

<p><strong>Queries</strong></p>

<dl>
  <dt>/SET autocreate_own_query ON</dt>
  <dd>Should new query window be created when you send message to someone (with <code>/MSG</code>).</dd>
  <dt>/SET autocreate_query_level MSGS</dt>
  <dd>New query window should be created when receiving messages with this level. MSGS, DCCMSGS and NOTICES levels work currently. You can disable this with <code>/SET -clear autocreate_query_level</code>.</dd>
  <dt>/SET autoclose_query 0</dt>
  <dd>Query windows can be automatically closed after certain time of inactivity. Queries with unread messages aren’t closed and active window is neither never closed. The value is given in seconds.</dd>
</dl>

<p><strong>Windows</strong></p>

<dl>
  <dt>/SET use_msgs_window OFF</dt>
  <dd>Create messages window at startup. All private messages go to this window. This only makes sense if you’ve disabled automatic query windows. Message window can also be created manually with /WINDOW LEVEL MSGS, /WINDOW NAME (msgs).</dd>
  <dt>/SET use_status_window ON</dt>
  <dd>Create status window at startup. All messages that don’t really have better place go here, like all /WHOIS replies etc. Status window can also be created manually with <code>/WINDOW LEVEL ALL -MSGS</code>, <code>/WINDOW NAME (status)</code>.</dd>
  <dt>/SET autocreate_windows ON</dt>
  <dd>Should we create new windows for new window items or just place everything in one window</dd>
  <dt>/SET autoclose_windows ON</dt>
  <dd>Should window be automatically closed when the last item in them is removed (ie. <code>/PART</code>, <code>/UNQUERY</code>).</dd>
  <dt>/SET reuse_unused_windows OFF</dt>
  <dd>When finding where to place new window item (channel, query) Irssi first tries to use already existing empty windows. If this is set ON, new window will always be created for all window items. This setting is ignored if autoclose_windows is set ON.</dd>
  <dt>/SET window_auto_change OFF</dt>
  <dd>Should Irssi automatically change to automatically created windows - usually queries when someone sends you a message. To prevent accidentally sending text meant to some other channel/nick, Irssi clears the input buffer when changing the window. The text is still in scrollback buffer, you can get it back with pressing arrow up key.</dd>
  <dt>/SET print_active_channel OFF</dt>
  <dd>When you keep more than one channel in same window, Irssi prints the messages coming to active channel as <code>&lt;nick&gt; text</code> and other channels as <code>&lt;nick:channel&gt; text</code>. If this setting is set ON, the messages to active channels are also printed in the latter way.</dd>
  <dt>/SET window_history OFF</dt>
  <dd>Should command history be kept separate for each window.</dd>
</dl>

<p><strong>User information</strong></p>

<dl>
  <dt>/SET nick</dt>
  <dd>Your nick name</dd>
  <dt>/SET alternate_nick</dt>
  <dd>Your alternate nick.</dd>
  <dt>/SET user_name</dt>
  <dd>Your username, if you have ident enabled this doesn’t affect anything</dd>
  <dt>/SET real_name</dt>
  <dd>Your real name.</dd>
</dl>

<p><strong>Server information</strong></p>

<dl>
  <dt>/SET skip_motd OFF</dt>
  <dd>Should we hide server’s MOTD (Message Of The Day).</dd>
  <dt>/SET server_reconnect_time 300</dt>
  <dd>Seconds to wait before connecting to same server again. Don’t set this too low since it usually doesn’t help at all - if the host is down, the few extra minutes of waiting won’t hurt much.</dd>
  <dt>/SET lag_max_before_disconnect 300</dt>
  <dd>Maximum server lag in seconds before disconnecting and trying to reconnect. This happens mostly only when network breaks between you and IRC server.</dd>
</dl>

<p><strong>Appearance</strong></p>

<dl>
  <dt>/SET timestamps ON</dt>
  <dd>Show timestamps before each message.</dd>
  <dt>/SET hide_text_style OFF</dt>
  <dd>Hide all bolds, underlines, MIRC colors, etc.</dd>
  <dt>/SET show_nickmode ON</dt>
  <dd>Show the nick’s mode before nick in channels, ie. ops have <code>&lt;@nick&gt;</code>, voices <code>&lt;+nick&gt;</code> and others <code>&lt; nick&gt;</code></dd>
  <dt>/SET show_nickmode_empty ON</dt>
  <dd>If the nick doesn’t have a mode, use one space. ie. ON: <code>&lt; nick&gt;</code>, OFF: <code>&lt;nick&gt;</code></dd>
  <dt>/SET show_quit_once OFF</dt>
  <dd>Show quit message only once in some of the channel windows the nick was in instead of in all windows.</dd>
  <dt>/SET lag_min_show 100</dt>
  <dd>Show the server lag in status bar if it’s bigger than this, the unit is 1/100 of seconds (ie. the default value of 100 = 1 second).</dd>
  <dt>/SET indent 10</dt>
  <dd>When lines are longer than screen width they have to be split to multiple lines. This specifies how much space to put at the beginning of the line before the text begins. This can be overridden in text formats with <code>%|</code> format.</dd>
  <dt>/SET activity_hide_targets</dt>
  <dd>If you don’t want to see window activity in some certain channels or queries, list them here. For example <code>#boringchannel =bot1 =bot2</code>. If any highlighted text or message for you appears in that window, this setting is ignored and the activity is shown.</dd>
</dl>

<p><strong>Nick completion</strong></p>

<dl>
  <dt>/SET completion_auto OFF</dt>
  <dd>Automatically complete the nick if line begins with start of nick and the completion character. Learn to use the tab-completion instead, it’s a lot better ;)</dd>
  <dt>/SET completion_char :</dt>
  <dd>Completion character to use.</dd>
</dl>

<h3 id="for-all-the-ircii-people">For all the ircII people</h3>

<p>I don’t like automatic query windows, I don’t like status window, I do like msgs window where all messages go:</p>

<div><div><pre><code>/SET autocreate_own_query OFF
/SET autocreate_query_level DCCMSGS
/SET use_status_window OFF
/SET use_msgs_window ON
</code></pre></div></div>

<p>Disable automatic window closing when <code>/PART</code>ing channel or <code>/UNQUERY</code>ing query:</p>

<div><div><pre><code>/SET autoclose_windows OFF
/SET reuse_unused_windows ON
</code></pre></div></div>

<p>Here’s the settings that make irssi work exactly like ircII in window management (send me a note if you can think of more):</p>

<div><div><pre><code>/SET autocreate_own_query OFF
/SET autocreate_query_level NONE
/SET use_status_window OFF
/SET use_msgs_window OFF
/SET reuse_unused_windows ON
/SET windows_auto_renumber OFF

/SET autostick_split_windows OFF
/SET autoclose_windows OFF
/SET print_active_channel ON
</code></pre></div></div>

<h2 id="statusbar">12. Statusbar</h2>

<p><code>/STATUSBAR</code> displays a list of the current statusbars, along with their position and visibility:</p>

<div><div><pre><code> Name                           Type   Placement Position Visible
 window                         window bottom    0        always
 window_inact                   window bottom    1        inactive
 prompt                         root   bottom    100      always
 topic                          root   top       1        always
</code></pre></div></div>

<p><code>/STATUSBAR &lt;name&gt;</code> prints the statusbar settings (type, placement, position, visibility) as well as its items. <code>/STATUSBAR &lt;name&gt; ENABLE|DISABLE</code> enables/disables the statusbar. <code>/STATUSBAR &lt;name&gt; RESET</code> resets the statusbar to its default settings, or if the statusbar was created by you, it will be removed.</p>

<p>The statusbar type can be either window or root. If the type is window, then a statusbar will be created for each split window, otherwise it will be created only once. Placement can be top or bottom, which refers to the top or bottom of the screen. Position is a number, the higher the value the lower it will appear in-screen. Visible can be always, active or inactive. Active/inactive is useful only with split windows; one split window is active and the rest are inactive. To adjust these settings, the following commands are available:</p>

<div><div><pre><code>/STATUSBAR &lt;name&gt; TYPE window|root
/STATUSBAR &lt;name&gt; PLACEMENT top|bottom
/STATUSBAR &lt;name&gt; POSITION &lt;num&gt;
/STATUSBAR &lt;name&gt; VISIBLE always|active|inactive
</code></pre></div></div>

<p>Statusbar items can also be added or removed via command. Note that when loading new statusbar scripts that add items, you will need to specify where you want to show the item and how it is aligned. This can be accomplished using the below commands:</p>

<div><div><pre><code>/STATUSBAR &lt;name&gt; ADD [-before | -after &lt;item&gt;] [-priority #] [-alignment left|right] &lt;item&gt;
/STATUSBAR &lt;name&gt; REMOVE &lt;item&gt;
</code></pre></div></div>

<p>For statusbar scripts, the item name is usually equivalent to the script name. The documentation of the script ought to tell you if this is not the case. For example, to add mail.pl before the window activity item, use: <code>/STATUSBAR window ADD -before act mail</code>.</p>
