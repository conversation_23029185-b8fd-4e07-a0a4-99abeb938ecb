How to submit a good bug report?

Bugs are tracked via the GitHub issue tracker. Open an issue at:
https://github.com/irssi/irssi/issues

First you should give the following information:
 - irssi version, if git (or devel. tarball) then which commit hash (or date
   from /version)?
 - operating system / distribution and it's version
 - when did it crash? did you do something? can you reproduce the crash?

Getting backtrace of the crash also helps a lot, especially if irssi crashes
randomly. If after crash you see text:

 "segmentation fault (core dumped)"

It writes a file named "core" or "irssi.core" depending on your OS to
directory where you started irssi. If it doesn't print the "(core dumped)"
or you can't find the core file, you'll have to raise the limit for max.
core file size before running irssi. To do this, say:

 ulimit -c unlimited

So, if you have the core file and GNU debugger (gdb), you can get the
backtrace with:

 gdb irssi core
 set logging file bt.log
 set logging on
 bt
 set logging off
 quit

Provide the bt.log file.

Here's an example session:

[cras@hurina] ~/cvs/m/irssi/src/fe-text$ gdb ./irssi core

GNU gdb 5.0
Copyright 2000 Free Software Foundation, Inc.
GDB is free software, covered by the GNU General Public License, and you are
welcome to change it and/or distribute copies of it under certain conditions.
Type "show copying" to see the conditions.
There is absolutely no warranty for GDB.  Type "show warranty" for details.
This GDB was configured as "i686-pc-linux-gnu"...

Core was generated by ./irssi'.
Program terminated with signal 11, Segmentation fault.
#0  0x805e949 in view_scroll (view=0x816cfb5, lines=0x816cfd9, 
    subline=0x816cfdd, scrollcount=-11, draw_nonclean=1)
    at textbuffer-view.c:528
528                             realcount += view->bottom_subline;

(gdb) bt

#0  0x805e949 in view_scroll (view=0x816cfb5, lines=0x816cfd9, 
    subline=0x816cfdd, scrollcount=-11, draw_nonclean=1)
    at textbuffer-view.c:528
#1  0x805ecb4 in textbuffer_view_scroll (view=0x816cfb5, lines=-11)
    at textbuffer-view.c:669
#2  0x8058387 in gui_window_scroll (window=0x816cead, lines=-11)
    at gui-windows.c:128
#3  0x8056b64 in window_prev_page () at gui-readline.c:109
#4  0x8057047 in key_scroll_backward () at gui-readline.c:334
...
(gdb) 
