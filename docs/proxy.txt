Irssi proxy usage:

First you'll need to have the proxy module installed, either configure
irssi with --with-proxy and do make install, or manually:

  cd src/irc/proxy
  make
  mkdir ~/.irssi/modules
  cp .libs/libproxy.so ~/.irssi/modules/

In irssi, say:

  /LOAD proxy

If you want the proxy to be loaded automatically at startup, add the
load command to ~/.irssi/startup:

  echo "load proxy" >> ~/.irssi/startup

You really should set some password for the proxy with:

  /SET irssiproxy_password secret

Then you'll need to configure the ports/ircnets the proxy listens in,
something like:

  /SET irssiproxy_ports IRCnet=2777 EFNet=2778 liberachat=2779

There we have 3 different irc networks answering in 3 ports. Note that
you'll have to make the correct /IRCNET ADD and /SERVER ADD commands to
make it work properly.

The special network name "?" allows the client to select the network
dynamically on connect:

  /SET irssiproxy_ports ?=2777

Now the client can send <network>:<password> as the server password, e.g.

  /CONNECT ... 2777 efnet:secret

to connect to efnet. If there is no irssiproxy_password set, you can
omit the ":" and just send the network name as the password.

By default, the proxy binds to all available interfaces. To make it
only listen on (for example) the loopback address:

  /SET irssiproxy_bind 127.0.0.1

Note that bind address changes won't take effect until the proxy is
disabled and then reenabled.

Once everything is set up, you can enable / disable the proxy:

  /TOGGLE irssiproxy

When the proxy is configured and running, the following command will
show all the currently connected clients:

  /IRSSIPROXY status
