
%9Syntax:%9

@SYNTAX:list@

%9Parameters:%9

    -yes:    Confirms that you want to receive a large amount of data.

    If the exact name of a channel is given, the only information about this
    channel is requested; otherwise, a list of all channels will be displayed.

%9Description:%9

    Displays the channel names that match your request; requesting all channels
    may cause the server to disconnect you for flooding.

%9Examples:%9

    /LIST
    /LIST -yes
    /LIST #ubuntu
    /LIST #*ubuntu*,>1

%9Remarks:%9

    Not all networks support server-side filtering. Some provide a network
    service or service bot instead; on IRCnet, you may use the List service:

    /SQUERY Alis HELP

    Other networks with service bots (like ChanServ) may also provide a list
    service bot (confirm with /WHOIS ALIS):

    /MSG Alis HELP

%9See also:%9 STATS, SQUERY, WHOIS

