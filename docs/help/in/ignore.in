
%9Syntax:%9

@SYNTAX:ignore@

%9Parameters:%9

    -regexp:      Indicates that the pattern is a regular expression.
    -full:        Indicates that the pattern must match a full word.
    -pattern:     The text pattern to ignore.
    -except:      Negates the ignore.
    -replies:     Also ignore nicknames who are talking to anyone who matches
                  the ignore.
    -network:     Ignores only on a specific network.
    -channels:    Ignores only on specific channels.
    -time:        The timeout to automatically remove the ignore.
                  Accepts units specified in days, hours, minutes, seconds,
                  milliseconds, or no unit for seconds.

    The mask, channels and levels to ignore; if no argument is provided, the
    list of ignores will be displayed.

%9Description:%9

    Ignores nicknames or text that matches a pattern.

    The special level 'NO_ACT' can be used to ignore activity in the statusbar
    without actually ignoring the message; this behavior is somewhat special
    because it is allowed in addition to other ignores for the same target.
    The special level 'HIDDEN' can be used to hide matching messages that can
    later be revealed using /WINDOW HIDELEVEL -HIDDEN
    The special level 'NOHILIGHT' can be used to suppress hilights without actually
    ignoring the message.

%9Examples:%9

    /IGNORE
    /IGNORE * JOINS
    /IGNORE * CTCPS
    /IGNORE -except *!*@*.irssi.org CTCPS
    /IGNORE #irssi ALL -PUBLIC -ACTIONS
    /IGNORE -replies *!*@*.irssi.org ALL
    /IGNORE -regexp -pattern (away|gone|back|playing|returned) * ACTIONS
    /IGNORE -regexp -pattern (away|gone|back|playing|returned) #channel ACTIONS
    /IGNORE *zzz* NICKS
    /IGNORE *afk* NICKS
    /IGNORE *away* NICKS
    /IGNORE #irssi NO_ACT JOINS PARTS QUITS
    /IGNORE mike NO_ACT -MSGS
    /IGNORE mike HIDDEN PUBLIC JOINS PARTS QUITS
    /IGNORE -time 5days christmas PUBLICS
    /IGNORE -time 300 mike PUBLICS

%9See also:%9 ACCEPT, SILENCE, UNIGNORE

