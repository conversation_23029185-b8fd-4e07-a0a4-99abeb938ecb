
%9Syntax:%9

@SYNTAX:mircdcc@

%9Parameters:%9

    ON:     Enables mIRC compatibility mode.
    OFF:    Disables mIRC compatibility mode.

%9Description:%9

    After establishing a DCC CHAT connection that you initiated, you might
    encounter some protocol issues if the target is using mIRC.

    If you or your target sees some unexpected output or behavior inside a DCC
    CHAT session, use this command to enable mIRC compatibility mode.

    When receiving a connection from an mIRC user, the compatibility mode will
    automatically be enabled.

%9Examples:%9

    /MIRCDCC ON
    /MIRCDCC OFF

%9See also:%9 ACTION, CTCP, DCC

