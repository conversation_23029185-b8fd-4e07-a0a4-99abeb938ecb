
%9Syntax:%9

@SYNTAX:connect@

%9Parameters:%9

    -4:                 Connects using IPv4.
    -6:                 Connects using IPv6.
    -tls:               Connects using TLS encryption.
    -tls_cert:          The TLS client certificate file.
    -tls_pkey:          The TLS client private key, if not included in the certificate file.
    -tls_pass:          The password for the TLS client private key or certificate.
    -tls_verify:        Verifies the TLS certificate of the server.
    -tls_cafile:        The file with the list of CA certificates.
    -tls_capath:        The directory which contains the CA certificates.
    -tls_ciphers:       TLS cipher suite preference lists.
    -tls_pinned_cert:   Pinned x509 certificate fingerprint.
    -tls_pinned_pubkey: Pinned public key fingerprint.
    -nocap:             Disable CAPREQ during connect
    -noproxy:           Ignores the global proxy configuration.
    -network:           The network this connection belongs to.
    -host:              The hostname you would like to connect from.
    -rawlog:            Immediately open rawlog after connecting.
    -!:                 Doesn't autojoin channels.
    -noautosendcmd:     Doesn't execute autosendcmd.

    A network or server to connect to; you can optionally specify a custom port,
    password and nickname.

%9Description:%9

    Opens a new connection to the specified network or server; existing
    connections are kept.

%9Examples:%9

    /CONNECT liberachat
    /CONNECT -6 liberachat
    /CONNECT -4 -! -host staff.irssi.org -network liberachat irc.libera.chat
    /CONNECT irc.irssi.org 6667 WzerT8zq mike

%9See also:%9 DISCONNECT, RMRECONNS, SERVER

