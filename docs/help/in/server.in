
%9Syntax:%9

@SYNTAX:server@

%9Parameters:%9

    LIST:               Displays the list of servers you have configured.
    CONNECT:            Connects to the given server.
    ADD:                Adds a server to your configuration.
    MODIFY:             Modifies a server in your configuration.
    REMOVE:             Removes a server from your configuration.
    PURGE:              Purges the commands queued to be sent to the server.

    -!:                 Doesn't autojoin the channels.
    -4:                 Connects using IPv4.
    -6:                 Connects using IPv6.
    -tls:               Connects using TLS encryption.
    -notls:             Connect without TLS encrption.
    -tls_cert:          The TLS client certificate file.
    -tls_pkey:          The TLS client private key, if not included in the
                        certificate file.
    -tls_pass:          The password for the TLS client private key or certificate.
    -tls_verify:        Verifies the TLS certificate of the server.	
    -notls_verify:      Doesn't verify the TLS certificate of the server.
    -tls_cafile:        The file with the list of CA certificates.
    -tls_capath:        The directory which contains the CA certificates.
    -tls_ciphers:       TLS cipher suite preference lists.
    -tls_pinned_cert:   Pinned x509 certificate fingerprint.
    -tls_pinned_pubkey: Pinned public key fingerprint.
    -auto:              Automatically connects to the server on startup.
    -noauto:            Doesn't connect to the server on startup.
    -cap:               Enable CAPREQ for server.
    -nocap:             Disable CAPREQ for server.
    -network:           The network the server belongs to.
    -host:              The hostname you would like to connect from.
    -cmdspeed:          Specifies the minimum amount of time, expressed in
                        milliseconds, that the client must wait before sending
                        additional commands to the server.
    -cmdmax:            Specifies the maximum number of commands to perform
                        before starting the internal flood protection.
    -port:              Specifies the port to connect to the server.
    -noproxy:           Ignores the global proxy configuration.
    -rawlog:            Immediately open rawlog after connecting.
    -noautosendcmd:     Doesn't execute autosendcmd.

    The server, port and network to add, modify or remove; if no argument is
    given, the list of servers you are connected to will be returned.

%9Description:%9

    Displays, adds, modifies or removes the network configuration of IRC
    servers.

    When using the ADD parameter on a server that already exists, the
    configuration will be merged with each other.

    When using the CONNECT parameter, it will connect to the specified
    server; the server in the active window will be disconnected
    unless you prepend the server with the '+' character.

    Specify '-' as password to remove a server password

%9Examples:%9

    /SERVER
    /SERVER CONNECT irc.libera.chat
    /SERVER CONNECT +irc.libera.chat
    /SERVER ADD -network liberachat -noautosendcmd irc.libera.chat
    /SERVER ADD -! -auto -host staff.irssi.org -4 -network liberachat -noproxy irc.libera.chat 6667
    /SERVER MODIFY -network liberachat -noauto irc.libera.chat
    /SERVER MODIFY -network liberachat irc.libera.chat 6697 -
    /SERVER REMOVE irc.libera.chat 6667 liberachat
    /SERVER PURGE
    /SERVER PURGE irc.libera.chat

%9See also:%9 CHANNEL, CONNECT, DISCONNECT, NETWORK, RECONNECT, RMRECONNS

