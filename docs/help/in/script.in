
%9Syntax:%9

@SYNTAX:script@

%9Parameters:%9

    LIST:          Displays the list of loaded scripts.
    EXEC:          Executes the given code.
    LOAD:          Loads the given script into the memory and executes it.
    UNLOAD:        Unloads the given script from the memory.
    RESET:         Unloads all the scripts.
    -permanent:    In combination with EXEC, the code will be loaded into the
                   memory.
    -autorun:      When passed to RESET the scripts in the autorun folder are
                   reloaded.

    If no argument is given, the list of active scripts will be displayed.

%9Description:%9

    Interact with the Perl engine to execute scripts.

%9Examples:%9

    /SCRIPT
    /SCRIPT LIST
    /SCRIPT LOAD ~/.irssi/scripts/nickserv.pl
    /SCRIPT UNLOAD nickserv
    /SCRIPT RESET
    /SCRIPT EXEC foreach my $channel (Irssi::channels()) { Irssi::print($channel->{name} . ' @ ' . $channel->{server}->{tag}); }

%9See also:%9 LOAD, SAVE, UNLOAD

