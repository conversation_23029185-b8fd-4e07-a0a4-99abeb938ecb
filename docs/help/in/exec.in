
%9Syntax:%9

@SYNTAX:exec@

%9Parameters:%9

    -:               Suppresses the process termination notification.
    -nosh:           Doesn't execute the command through /bin/sh.
    -out:            Sends the output to the active channel or query.
    -msg:            Sends the output to the specified nickname or channel.
    -notice:         Sends the output to the specified nickname or channel as
                     notices.
    -name:           Gives the process the specified name.
    -window:         Displays the output in the active window.
    -close:          Forcibly closes a process that doesn't die.
    -<signal>:       Sends the given signal to the process.
    -in:             Sends text to the standard input of the process.
    -interactive:    Executes the process in a new window item.

    The command to execute; if no output parameter is given, the active window
    will be used and if no parameters are given at all, the list of active
    processes will be displayed.

%9Description:%9

    Executes the specified command in the background; the process can be
    accessed by its id or the name you gave it.

    The output of the process can be redirected to various targets, such as
    a window, a channel, a nickname or a query.

    The process identifier must always begin with the '%%' character. For
    example %%0.

    If you remove a process with the close parameter, it will only make <PERSON><PERSON><PERSON>
    detach from it; the process will keep running until it terminates.

%9Examples:%9

    /EXEC
    /EXEC ls
    /EXEC -msg #irssi cat unicorn.txt
    /EXEC -out cat /etc/passwd | grep $USER | awk -F: '{print $5}'
    /EXEC -name ssh -nosh -interactive -window ssh staff.irssi.org
    /EXEC -close mailserver
    /EXEC -close %%0

%9See also:%9 CAT, CD, ECHO, EVAL

