
%9Syntax:%9

@SYNTAX:statusbar@

%9Parameters:%9

    ADD:          Adds a statusbar to the list of statusbars.
    MODIFY:       Modifies the configuration of a statusbar.
    RESET:        Restores the default statusbar configuration.
    ADDITEM:      Adds an item to the specified statusbar. It can be set to
                  appear before/after another item and left/right aligned
                  on the screen.
    MODIFYITEM:   Changes an item position inside a bar.
    REMOVEITEM:   Removes an item from the specified statusbar.
    INFO:         List the current details and items of the specified
                  statusbar.

    -disable:     Removes a statusbar from the list.
    -type:        Sets the type of statusbar, for each split window or only
                  once at the root (very top or bottom) of the screen.
    -placement:   Sets the placement of the statusbar, either at the top or
                  the bottom of the screen or split window.
    -position:    Sets the position of the statusbar. Represented as a
                  number, with smaller numbers implying a position further
                  to the top.
    -visible:     Sets the visibility of the statusbar. If set to always,
                  it is visible on all split windows, otherwise if set to
                  inactive or active then it is only visible on inactive or
                  active split windows, respectively.
    -before:      This item is added before the other item.
    -after:       This item is added after the other item.
    -priority:    When the statusbar items overflow, the item with the
                  lowest priority is removed or truncated first.
                  Priority can be negative, in which case it'll have to be
                  quoted (e.g. -priority "-1")
    -alignment:   Display the item on the right side.

    Where statusbar refers to the name of the statusbar; if no
    argument is given, or `LIST` is given, the entire list of
    statusbars along with a quick overview of their properties will be
    displayed.

%9Description:%9

    Allows adjustment of the attributes and items of a statusbar, as well
    as where it is located and whether or not it is currently visible.

%9Examples:%9

    /STATUSBAR
    /STATUSBAR INFO window
    /STATUSBAR REMOVEITEM time window
    /STATUSBAR ADDITEM time window
    /STATUSBAR RESET window
    /STATUSBAR MODIFY -disable topic
    /STATUSBAR MODIFY -nodisable topic

%9Remarks:%9

    Statusbar syntax was changed in Irssi 1.2. The old syntax is still
    accepted for backward compatibility, but no longer documented.

%9See also:%9 WINDOW

