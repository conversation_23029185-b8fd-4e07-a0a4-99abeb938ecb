
%9Syntax:%9

@SYNTAX:whois@

%9Parameters:%9

    -<server tag>:    The server tag the target nickname is on.

    The remote server to query and the nicknames; if no remote server is given,
    the server you are connected to will be used. If no nickname is given, you
    will query yourself.

%9Description:%9

    Displays information about users in the specified channel; you may give the
    same nickname as the argument twice to also query the idle time.

    If the nickname is not online, the WHOWAS command will be automatically
    performed.

%9Examples:%9

    /WHOIS
    /WHOIS mike
    /WHOIS ircsource.irssi.org bob
    /WHOIS sarah sarah

%9See also:%9 CHANNEL, NAMES, WHO, WHOWAS

