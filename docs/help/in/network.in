
%9Syntax:%9

@SYNTAX:network@

%9Parameters:%9

    LIST:            Displays the list of configured networks.
    ADD:             Adds a network to your configuration.
    MODIFY:          Modifies a network in your configuration.
    REMOVE:          Removes a network from your configuration.

    -nick:           Specifies the nickname to use.
    -alternate_nick  Specifies the alternate nickname to use.
    -user:           Specifies the user identity to use.
    -realname:       Specifies the real name to use.
    -host:           Specifies the hostname to use.
    -usermode:       Specifies the user modes to set on yourself.
    -autosendcmd:    Specifies the commands, separated by the ';' character,
                     and enclosed within two "'" characters, to perform after
                     connecting. 
                     (Some characters need to be escaped - see /help eval)
    -querychans:     Specifies the maximum number of channels to put in one MODE
                     or WHO command when synchronizing.
    -whois:          Specifies the maximum number of nicknames in one WHOIS
                     command.
    -msgs:           Specifies the maximum number of nicknames in one PRIVMSG
                     command.
    -kicks:          Specifies the maximum number of nicknames in one KICK
                     command.
    -modes:          Specifies the maximum number of nicknames in one MODE
                     command.
    -cmdspeed:       Specifies the minimum amount of time, expressed in
                     milliseconds, that the client must wait before sending
                     additional commands to the server.
    -cmdmax:         Specifies the maximum number of commands to perform before
                     starting the internal flood protection.
    -sasl_mechanism  Specifies the mechanism to use for the SASL authentication.
                     Irssi supports: PLAIN, EXTERNAL, SCRAM-SHA-1, SCRAM-SHA-256
                     and SCRAM-SHA-512
                     Use '' to disable the authentication.
    -sasl_username   Specifies the username to use during the SASL authentication.
    -sasl_password   Specifies the password to use during the SASL authentication.


    The name of the network to add, edit or remove; if no parameter is given,
    the list of networks will be displayed.

%9Description:%9

    Displays, adds, modifies or removes the network configuration of IRC
    networks.

    When using the ADD parameter on a network that already exists, the
    configuration will be merged with each other.

    We recommend using 'WAIT 2000' between the automated commands in order to
    prevent you from being kicked from the network due to flooding commands.

%9Examples:%9

    /NETWORK ADD -usermode +giw EFnet
    /NETWORK ADD -usermode +iw -nick mike -realname 'The one and only mike!' -host staff.irssi.org liberachat
    /NETWORK ADD -autosendcmd '^MSG NickServ identify WzerT8zq' liberachat
    /NETWORK ADD -autosendcmd '^MSG <EMAIL> AUTH mike WzerT8zq; WAIT 2000; OPER mike WzerT8zq; WAIT 2000; MODE mike +kXP' Quakenet
    /NETWORK MODIFY -usermode +gi EFnet
    /NETWORK REMOVE liberachat

%9See also:%9 CHANNEL, CONNECT, SERVER

