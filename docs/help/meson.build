install_data(
  files(
    'accept',
    'action',
    'admin',
    'alias',
    'away',
    'ban',
    'beep',
    'bind',
    'cat',
    'cd',
    'channel',
    'clear',
    'completion',
    'connect',
    'ctcp',
    'cycle',
    'dcc',
    'dehilight',
    'deop',
    'devoice',
    'die',
    'disconnect',
    'echo',
    'eval',
    'exec',
    'flushbuffer',
    'format',
    'hash',
    'help',
    'hilight',
    'ignore',
    'info',
    'invite',
    'irssiproxy',
    'ison',
    'join',
    'kick',
    'kickban',
    'kill',
    'knock',
    'knockout',
    'lastlog',
    'layout',
    'levels',
    'links',
    'list',
    'load',
    'log',
    'lusers',
    'map',
    'me',
    'mircdcc',
    'mode',
    'motd',
    'msg',
    'names',
    'nctcp',
    'netsplit',
    'network',
    'nick',
    'notice',
    'notify',
    'op',
    'oper',
    'otr',
    'part',
    'ping',
    'query',
    'quit',
    'quote',
    'rawlog',
    'recode',
    'reconnect',
    'rehash',
    'reload',
    'restart',
    'rmreconns',
    'rmrejoins',
    'save',
    'sconnect',
    'script',
    'scrollback',
    'server',
    'servlist',
    'set',
    'silence',
    'squery',
    'squit',
    'stats',
    'statusbar',
    'time',
    'toggle',
    'topic',
    'trace',
    'ts',
    'unalias',
    'unban',
    'unignore',
    'unload',
    'unnotify',
    'unquery',
    'unsilence',
    'upgrade',
    'uptime',
    'userhost',
    'ver',
    'version',
    'voice',
    'wait',
    'wall',
    'wallops',
    'who',
    'whois',
    'whowas',
    'window',
  ),
  install_dir : helpdir)

# subdir('in')
